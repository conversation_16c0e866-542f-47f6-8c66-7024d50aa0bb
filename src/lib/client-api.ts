/**
 * Client-side API service for static deployment
 * This replaces server-side API routes when deploying to static hosting
 */

import { Client, Databases, Storage, Account, Query, ID } from 'appwrite';

// Initialize Appwrite client for browser
const client = new Client();

client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'http://localhost/v1')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '');

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

// Database and collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '';
export const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || '',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || '',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || '',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || '',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || '',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || '',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || '',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || '',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || '',
};

// Storage bucket ID
export const STORAGE_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID || '';

// API Functions for client-side usage
export const clientAPI = {
  // Products
  async getProducts(limit = 50, offset = 0) {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        [
          Query.limit(limit),
          Query.offset(offset),
          Query.orderDesc('$createdAt')
        ]
      );
      return { success: true, data: response };
    } catch (error) {
      console.error('Error fetching products:', error);
      return { success: false, error: error.message };
    }
  },

  async getProduct(id: string) {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        id
      );
      return { success: true, data: response };
    } catch (error) {
      console.error('Error fetching product:', error);
      return { success: false, error: error.message };
    }
  },

  // Categories
  async getCategories() {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        [Query.orderAsc('name')]
      );
      return { success: true, data: response };
    } catch (error) {
      console.error('Error fetching categories:', error);
      return { success: false, error: error.message };
    }
  },

  // Tags
  async getTags() {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        [Query.orderAsc('name')]
      );
      return { success: true, data: response };
    } catch (error) {
      console.error('Error fetching tags:', error);
      return { success: false, error: error.message };
    }
  },

  // Contact form submission
  async submitContact(data: {
    name: string;
    email: string;
    company?: string;
    message: string;
    productId?: string;
  }) {
    try {
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        ID.unique(),
        {
          ...data,
          status: 'new',
          createdAt: new Date().toISOString(),
        }
      );
      return { success: true, data: response };
    } catch (error) {
      console.error('Error submitting contact form:', error);
      return { success: false, error: error.message };
    }
  },

  // Authentication (for admin dashboard)
  async login(email: string, password: string) {
    try {
      const session = await account.createEmailPasswordSession(email, password);
      return { success: true, data: session };
    } catch (error) {
      console.error('Error logging in:', error);
      return { success: false, error: error.message };
    }
  },

  async logout() {
    try {
      await account.deleteSession('current');
      return { success: true };
    } catch (error) {
      console.error('Error logging out:', error);
      return { success: false, error: error.message };
    }
  },

  async getCurrentUser() {
    try {
      const user = await account.get();
      return { success: true, data: user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // File upload
  async uploadFile(file: File, bucketId = STORAGE_BUCKET_ID) {
    try {
      const response = await storage.createFile(
        bucketId,
        ID.unique(),
        file
      );
      return { success: true, data: response };
    } catch (error) {
      console.error('Error uploading file:', error);
      return { success: false, error: error.message };
    }
  },

  // Get file URL
  getFileUrl(fileId: string, bucketId = STORAGE_BUCKET_ID) {
    return storage.getFileView(bucketId, fileId);
  }
};

export { Query, ID };
