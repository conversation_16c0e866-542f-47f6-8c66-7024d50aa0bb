// Accessibility utilities for WCAG 2.1 AA compliance

// Skip link component props
export interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

// Focus management utilities
export class FocusManager {
  private static focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ');

  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    return Array.from(container.querySelectorAll(this.focusableSelectors));
  }

  static trapFocus(container: HTMLElement) {
    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    
    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }

  static restoreFocus(element: HTMLElement | null) {
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  }
}

// ARIA utilities
export class AriaUtils {
  static announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  static generateId(prefix: string = 'element'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }

  static setAriaExpanded(element: HTMLElement, expanded: boolean) {
    element.setAttribute('aria-expanded', expanded.toString());
  }

  static setAriaHidden(element: HTMLElement, hidden: boolean) {
    element.setAttribute('aria-hidden', hidden.toString());
  }
}

// Color contrast utilities
export class ColorContrast {
  // Calculate relative luminance
  private static getRelativeLuminance(rgb: [number, number, number]): number {
    const [r, g, b] = rgb.map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  // Calculate contrast ratio between two colors
  static getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
    const lum1 = this.getRelativeLuminance(color1);
    const lum2 = this.getRelativeLuminance(color2);
    
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  }

  // Check if contrast ratio meets WCAG AA standards
  static meetsWCAGAA(color1: [number, number, number], color2: [number, number, number], isLargeText: boolean = false): boolean {
    const ratio = this.getContrastRatio(color1, color2);
    return isLargeText ? ratio >= 3 : ratio >= 4.5;
  }

  // Check if contrast ratio meets WCAG AAA standards
  static meetsWCAGAAA(color1: [number, number, number], color2: [number, number, number], isLargeText: boolean = false): boolean {
    const ratio = this.getContrastRatio(color1, color2);
    return isLargeText ? ratio >= 4.5 : ratio >= 7;
  }
}

// Keyboard navigation utilities
export class KeyboardNavigation {
  static readonly KEYS = {
    ENTER: 'Enter',
    SPACE: ' ',
    ESCAPE: 'Escape',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    ARROW_LEFT: 'ArrowLeft',
    ARROW_RIGHT: 'ArrowRight',
    HOME: 'Home',
    END: 'End',
    TAB: 'Tab',
  } as const;

  static handleMenuNavigation(
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onSelect?: (index: number) => void,
    onClose?: () => void
  ): number {
    let newIndex = currentIndex;

    switch (event.key) {
      case this.KEYS.ARROW_DOWN:
        event.preventDefault();
        newIndex = (currentIndex + 1) % items.length;
        break;
      case this.KEYS.ARROW_UP:
        event.preventDefault();
        newIndex = (currentIndex - 1 + items.length) % items.length;
        break;
      case this.KEYS.HOME:
        event.preventDefault();
        newIndex = 0;
        break;
      case this.KEYS.END:
        event.preventDefault();
        newIndex = items.length - 1;
        break;
      case this.KEYS.ENTER:
      case this.KEYS.SPACE:
        event.preventDefault();
        onSelect?.(currentIndex);
        break;
      case this.KEYS.ESCAPE:
        event.preventDefault();
        onClose?.();
        break;
    }

    if (newIndex !== currentIndex) {
      items[newIndex]?.focus();
    }

    return newIndex;
  }
}

// Screen reader utilities
export class ScreenReaderUtils {
  static createVisuallyHiddenText(text: string): HTMLSpanElement {
    const span = document.createElement('span');
    span.className = 'sr-only';
    span.textContent = text;
    return span;
  }

  static addScreenReaderDescription(element: HTMLElement, description: string): string {
    const descriptionId = AriaUtils.generateId('description');
    const descriptionElement = document.createElement('span');
    descriptionElement.id = descriptionId;
    descriptionElement.className = 'sr-only';
    descriptionElement.textContent = description;
    
    element.appendChild(descriptionElement);
    element.setAttribute('aria-describedby', descriptionId);
    
    return descriptionId;
  }
}

// Form accessibility utilities
export class FormAccessibility {
  static associateLabelWithInput(label: HTMLLabelElement, input: HTMLInputElement): void {
    const inputId = input.id || AriaUtils.generateId('input');
    input.id = inputId;
    label.setAttribute('for', inputId);
  }

  static addErrorMessage(input: HTMLInputElement, errorMessage: string): string {
    const errorId = AriaUtils.generateId('error');
    const errorElement = document.createElement('div');
    errorElement.id = errorId;
    errorElement.className = 'text-red-600 text-sm mt-1';
    errorElement.setAttribute('role', 'alert');
    errorElement.textContent = errorMessage;
    
    input.parentNode?.insertBefore(errorElement, input.nextSibling);
    input.setAttribute('aria-describedby', errorId);
    input.setAttribute('aria-invalid', 'true');
    
    return errorId;
  }

  static removeErrorMessage(input: HTMLInputElement, errorId: string): void {
    const errorElement = document.getElementById(errorId);
    if (errorElement) {
      errorElement.remove();
    }
    input.removeAttribute('aria-describedby');
    input.removeAttribute('aria-invalid');
  }
}

// Motion and animation preferences
export class MotionPreferences {
  static prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  static respectMotionPreferences(element: HTMLElement, animationClass: string): void {
    if (!this.prefersReducedMotion()) {
      element.classList.add(animationClass);
    }
  }
}
