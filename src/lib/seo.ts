import { Metadata } from 'next';

// Base SEO configuration
export const siteConfig = {
  name: 'AfricSource',
  title: 'AfricSource - Premium African Commodity Exports',
  description: 'Connecting global buyers with Africa\'s finest agricultural and mineral products. Trusted, transparent, and sustainable sourcing.',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://africsource.com',
  ogImage: '/og-image.jpg',
  creator: 'AfricSource',
  keywords: [
    'African exports',
    'commodities',
    'agricultural exports',
    'sustainable sourcing',
    'global trade',
    'African agriculture',
    'commodity trading',
    'export business',
    'sustainable farming',
    'fair trade',
    'organic products',
    'bulk commodities'
  ],
  authors: [
    {
      name: 'AfricSource',
      url: 'https://africsource.com',
    },
  ],
  social: {
    twitter: '@africsource',
    linkedin: 'company/africsource',
  },
};

// Default metadata
export const defaultMetadata: Metadata = {
  title: {
    default: siteConfig.title,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  authors: siteConfig.authors,
  creator: siteConfig.creator,
  publisher: siteConfig.creator,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(siteConfig.url),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.title,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.title,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: siteConfig.social.twitter,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_SITE_VERIFICATION,
  },
};

// Generate metadata for pages
export function generatePageMetadata({
  title,
  description,
  path = '',
  image,
  noIndex = false,
}: {
  title?: string;
  description?: string;
  path?: string;
  image?: string;
  noIndex?: boolean;
}): Metadata {
  const pageTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.title;
  const pageDescription = description || siteConfig.description;
  const pageUrl = `${siteConfig.url}${path}`;
  const pageImage = image || siteConfig.ogImage;

  return {
    title: pageTitle,
    description: pageDescription,
    alternates: {
      canonical: pageUrl,
    },
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: pageUrl,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: pageTitle,
        },
      ],
    },
    twitter: {
      title: pageTitle,
      description: pageDescription,
      images: [pageImage],
    },
    robots: noIndex
      ? {
          index: false,
          follow: false,
        }
      : undefined,
  };
}

// Generate product metadata
export function generateProductMetadata(product: {
  name: string;
  description: string;
  images?: Array<{ url: string; alt?: string }>;
  price?: string;
  categories?: Array<{ name: string }>;
  tags?: Array<{ name: string }>;
}): Metadata {
  const title = `${product.name} - Premium African Export`;
  const description = product.description.length > 160 
    ? `${product.description.substring(0, 157)}...`
    : product.description;
  
  const image = product.images?.[0]?.url || siteConfig.ogImage;
  
  const keywords = [
    ...siteConfig.keywords,
    product.name.toLowerCase(),
    ...(product.categories?.map(cat => cat.name.toLowerCase()) || []),
    ...(product.tags?.map(tag => tag.name.toLowerCase()) || []),
  ];

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: product.name,
        },
      ],
    },
    twitter: {
      title,
      description,
      images: [image],
    },
  };
}

// Generate structured data for organization
export function generateOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: `${siteConfig.url}/logo.png`,
    sameAs: [
      `https://twitter.com/${siteConfig.social.twitter.replace('@', '')}`,
      `https://linkedin.com/${siteConfig.social.linkedin}`,
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      availableLanguage: ['English'],
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'Multiple African Countries',
    },
  };
}

// Generate structured data for product
export function generateProductStructuredData(product: {
  name: string;
  description: string;
  price?: string;
  images?: Array<{ url: string }>;
  categories?: Array<{ name: string }>;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.images?.map(img => img.url) || [siteConfig.ogImage],
    brand: {
      '@type': 'Brand',
      name: siteConfig.name,
    },
    manufacturer: {
      '@type': 'Organization',
      name: siteConfig.name,
    },
    category: product.categories?.map(cat => cat.name).join(', '),
    offers: product.price ? {
      '@type': 'Offer',
      price: product.price.replace(/[^0-9.]/g, ''),
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: siteConfig.name,
      },
    } : undefined,
  };
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

// Generate FAQ structured data
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

// Generate local business structured data
export function generateLocalBusinessStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    '@id': `${siteConfig.url}#business`,
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: `${siteConfig.url}/logo.png`,
    image: `${siteConfig.url}/og-image.jpg`,
    telephone: process.env.NEXT_PUBLIC_PHONE || '+1-XXX-XXX-XXXX',
    email: process.env.NEXT_PUBLIC_EMAIL || '<EMAIL>',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'Multiple African Countries',
      addressRegion: 'Africa',
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: '0.0236',
      longitude: '37.9062',
    },
    openingHours: 'Mo-Fr 08:00-18:00',
    sameAs: [
      `https://twitter.com/${siteConfig.social.twitter.replace('@', '')}`,
      `https://linkedin.com/${siteConfig.social.linkedin}`,
    ],
  };
}

// Generate website structured data
export function generateWebsiteStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    '@id': `${siteConfig.url}#website`,
    url: siteConfig.url,
    name: siteConfig.name,
    description: siteConfig.description,
    publisher: {
      '@id': `${siteConfig.url}#organization`,
    },
    potentialAction: [
      {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${siteConfig.url}/products?search={search_term_string}`,
        },
        'query-input': 'required name=search_term_string',
      },
    ],
  };
}
