import { Linkedin, Twitter, Instagram, Facebook, Youtube } from "lucide-react";

// Social media platform configuration
export interface SocialPlatform {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  enabled: boolean;
}

// Get social media links from environment variables
export function getSocialMediaLinks(): SocialPlatform[] {
  const platforms: SocialPlatform[] = [
    {
      name: "LinkedIn",
      href: process.env.NEXT_PUBLIC_SOCIAL_LINKEDIN || "",
      icon: Linkedin,
      enabled: !!process.env.NEXT_PUBLIC_SOCIAL_LINKEDIN,
    },
    {
      name: "Twitter",
      href: process.env.NEXT_PUBLIC_SOCIAL_TWITTER || "",
      icon: Twitter,
      enabled: !!process.env.NEXT_PUBLIC_SOCIAL_TWITTER,
    },
    {
      name: "Instagram",
      href: process.env.NEXT_PUBLIC_SOCIAL_INSTAGRAM || "",
      icon: Instagram,
      enabled: !!process.env.NEXT_PUBLIC_SOCIAL_INSTAGRAM,
    },
    {
      name: "Facebook",
      href: process.env.NEXT_PUBLIC_SOCIAL_FACEBOOK || "",
      icon: Facebook,
      enabled: !!process.env.NEXT_PUBLIC_SOCIAL_FACEBOOK,
    },
    {
      name: "YouTube",
      href: process.env.NEXT_PUBLIC_SOCIAL_YOUTUBE || "",
      icon: Youtube,
      enabled: !!process.env.NEXT_PUBLIC_SOCIAL_YOUTUBE,
    },
  ];

  // Return only enabled platforms
  return platforms.filter(platform => platform.enabled);
}

// Fallback social media links (used when environment variables are not set)
export function getFallbackSocialLinks(): SocialPlatform[] {
  return [
    {
      name: "LinkedIn",
      href: "#",
      icon: Linkedin,
      enabled: true,
    },
    {
      name: "Twitter",
      href: "#",
      icon: Twitter,
      enabled: true,
    },
    {
      name: "Instagram",
      href: "#",
      icon: Instagram,
      enabled: true,
    },
    {
      name: "Facebook",
      href: "#",
      icon: Facebook,
      enabled: true,
    },
  ];
}

// Get social media links with fallback
export function getSocialLinksWithFallback(): SocialPlatform[] {
  const envLinks = getSocialMediaLinks();
  
  // If no environment variables are set, use fallback
  if (envLinks.length === 0) {
    return getFallbackSocialLinks();
  }
  
  return envLinks;
}
