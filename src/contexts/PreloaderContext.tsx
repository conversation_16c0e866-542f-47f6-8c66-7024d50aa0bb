"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

interface PreloaderContextType {
  isPreloaderVisible: boolean;
  hidePreloader: () => void;
  showPreloader: () => void;
}

const PreloaderContext = createContext<PreloaderContextType | undefined>(undefined);

export function PreloaderProvider({ children }: { children: React.ReactNode }) {
  const [isPreloaderVisible, setIsPreloaderVisible] = useState(true);

  const hidePreloader = () => {
    setIsPreloaderVisible(false);
  };

  const showPreloader = () => {
    setIsPreloaderVisible(true);
  };

  // Auto-hide preloader after a maximum time (fallback)
  useEffect(() => {
    const maxLoadTime = setTimeout(() => {
      hidePreloader();
    }, 8000); // 8 seconds maximum

    return () => clearTimeout(maxLoadTime);
  }, []);

  return (
    <PreloaderContext.Provider value={{
      isPreloaderVisible,
      hidePreloader,
      showPreloader
    }}>
      {children}
    </PreloaderContext.Provider>
  );
}

export function usePreloader() {
  const context = useContext(PreloaderContext);
  if (context === undefined) {
    throw new Error('usePreloader must be used within a PreloaderProvider');
  }
  return context;
}
