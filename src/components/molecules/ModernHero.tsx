"use client";

import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/atoms/Button';

interface ModernHeroProps {
  title: React.ReactNode;
  description: React.ReactNode;
  imageSrc: string;
  imageAlt: string;
  primaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonText?: string;
  secondaryButtonHref?: string;
  className?: string;
}

export function ModernHero({
  title,
  description,
  imageSrc,
  imageAlt,
  primaryButtonText,
  primaryButtonHref = '#',
  secondaryButtonText,
  secondaryButtonHref = '#',
  className = '',
}: ModernHeroProps) {
  return (
    <section className={`bg-white py-12 ${className}`}>
      <div className="container-custom mx-auto">
        <div className="grid max-w-screen-xl px-4 pt-8 pb-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
          <div className="mr-auto place-self-center lg:col-span-7">
            <h1 className="max-w-2xl mb-4 text-4xl font-extrabold leading-none tracking-tight text-earth-900 md:text-5xl xl:text-6xl">
              {title}
            </h1>
            
            <div className="max-w-2xl mb-6 font-light text-earth-600 lg:mb-8 md:text-lg lg:text-xl">
              {description}
            </div>
            
            <div className="space-y-4 sm:flex sm:space-y-0 sm:space-x-4">
              {primaryButtonText && (
                <Button
                  href={primaryButtonHref}
                  variant="primary"
                  size="lg"
                >
                  {primaryButtonText}
                </Button>
              )}
              
              {secondaryButtonText && (
                <Button
                  href={secondaryButtonHref}
                  variant="outline"
                  size="lg"
                >
                  {secondaryButtonText}
                </Button>
              )}
            </div>
          </div>
          
          <div className="hidden lg:mt-0 lg:col-span-5 lg:flex">
            <div className="relative w-full h-[400px]">
              <Image
                src={imageSrc}
                alt={imageAlt}
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Hero with background image
export function ModernHeroWithBackground({
  title,
  description,
  backgroundSrc,
  primaryButtonText,
  primaryButtonHref = '#',
  secondaryButtonText,
  secondaryButtonHref = '#',
  className = '',
}: Omit<ModernHeroProps, 'imageSrc' | 'imageAlt'> & { backgroundSrc: string }) {
  return (
    <section className={`relative overflow-hidden ${className}`}>
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src={backgroundSrc}
          alt="Background"
          fill
          priority
          className="object-cover"
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-earth-900/80 to-earth-900/40"></div>
      </div>
      
      <div className="container-custom mx-auto relative z-10">
        <div className="grid max-w-screen-xl px-4 py-16 mx-auto lg:py-24 lg:grid-cols-12 gap-8">
          <div className="mr-auto place-self-center lg:col-span-8">
            <h1 className="max-w-2xl mb-4 text-4xl font-extrabold leading-none tracking-tight text-white md:text-5xl xl:text-6xl">
              {title}
            </h1>
            
            <div className="max-w-2xl mb-6 font-light text-earth-100 lg:mb-8 md:text-lg lg:text-xl">
              {description}
            </div>
            
            <div className="space-y-4 sm:flex sm:space-y-0 sm:space-x-4">
              {primaryButtonText && (
                <Button
                  href={primaryButtonHref}
                  variant="primary"
                  size="lg"
                >
                  {primaryButtonText}
                </Button>
              )}
              
              {secondaryButtonText && (
                <Button
                  href={secondaryButtonHref}
                  variant="ghost"
                  size="lg"
                  className="text-white border-white hover:bg-white/10"
                >
                  {secondaryButtonText}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Hero with split design (text on left, image on right with colored background)
export function ModernHeroSplit({
  title,
  description,
  imageSrc,
  imageAlt,
  primaryButtonText,
  primaryButtonHref = '#',
  secondaryButtonText,
  secondaryButtonHref = '#',
  className = '',
}: ModernHeroProps) {
  return (
    <section className={`bg-white ${className}`}>
      <div className="container-custom mx-auto">
        <div className="grid max-w-screen-xl mx-auto lg:grid-cols-12">
          <div className="mr-auto place-self-center py-12 px-4 lg:col-span-6">
            <h1 className="max-w-2xl mb-4 text-4xl font-extrabold leading-none tracking-tight text-earth-900 md:text-5xl xl:text-6xl">
              {title}
            </h1>
            
            <div className="max-w-2xl mb-6 font-light text-earth-600 lg:mb-8 md:text-lg lg:text-xl">
              {description}
            </div>
            
            <div className="space-y-4 sm:flex sm:space-y-0 sm:space-x-4">
              {primaryButtonText && (
                <Button
                  href={primaryButtonHref}
                  variant="primary"
                  size="lg"
                >
                  {primaryButtonText}
                </Button>
              )}
              
              {secondaryButtonText && (
                <Button
                  href={secondaryButtonHref}
                  variant="outline"
                  size="lg"
                >
                  {secondaryButtonText}
                </Button>
              )}
            </div>
          </div>
          
          <div className="lg:col-span-6 bg-primary-50 flex items-center justify-center">
            <div className="relative w-full h-[500px] p-8">
              <Image
                src={imageSrc}
                alt={imageAlt}
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
