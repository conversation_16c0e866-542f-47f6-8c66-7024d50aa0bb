"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { ImageIcon } from 'lucide-react';

interface SafeImageProps {
  src: string;
  alt: string;
  fill?: boolean;
  className?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
}

export default function SafeImage({ 
  src, 
  alt, 
  fill = false, 
  className = '', 
  width, 
  height,
  onLoad,
  onError 
}: SafeImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  if (imageError) {
    return (
      <div className={`bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center ${className} border-2 border-dashed border-gray-300`}>
        <div className="text-center text-gray-500 p-4">
          <ImageIcon size={32} className="mx-auto mb-2 opacity-50" />
          <p className="text-xs font-medium">Image unavailable</p>
          <p className="text-xs opacity-75 mt-1">Please try refreshing</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {isLoading && (
        <div className={`bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse flex items-center justify-center ${className} absolute inset-0 z-10`}>
          <div className="text-center text-gray-400">
            <ImageIcon size={32} className="mx-auto mb-2 animate-pulse" />
            <div className="w-16 h-1 bg-gray-300 rounded-full mx-auto">
              <div className="h-1 bg-emerald-500 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          </div>
        </div>
      )}
      <Image
        src={src}
        alt={alt}
        fill={fill}
        width={!fill ? width : undefined}
        height={!fill ? height : undefined}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        unoptimized={src.includes('helevon-technology-appwrite')} // Disable optimization for Appwrite images
      />
    </>
  );
}
