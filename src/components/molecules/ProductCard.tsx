"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { cva, type VariantProps } from 'class-variance-authority';
import { Button } from '@/components/atoms/Button';

// Define product card variants
const productCardVariants = cva(
  "relative overflow-hidden transition-all duration-300 group",
  {
    variants: {
      variant: {
        default: "bg-white rounded-xl shadow-soft hover:shadow-lg",
        minimal: "bg-transparent",
        featured: "bg-white rounded-xl shadow-md hover:shadow-xl border-l-4 border-primary-500",
      },
      aspectRatio: {
        auto: "",
        square: "aspect-square",
        video: "aspect-video",
        portrait: "aspect-[3/4]",
      },
    },
    defaultVariants: {
      variant: "default",
      aspectRatio: "auto",
    },
  }
);

export interface ProductCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof productCardVariants> {
  product: {
    id: string;
    name: string;
    description: string;
    imageSrc: string;
    price?: string;
    category?: string;
    badge?: string;
    url?: string;
  };
  showDescription?: boolean;
  showPrice?: boolean;
  showCategory?: boolean;
  showButton?: boolean;
  buttonText?: string;
}

export function ProductCard({
  className,
  variant,
  aspectRatio,
  product,
  showDescription = true,
  showPrice = true,
  showCategory = true,
  showButton = true,
  buttonText = "View Details",
  ...props
}: ProductCardProps) {
  const {
    id,
    name,
    description,
    imageSrc,
    price,
    category,
    badge,
    url = `/products/${id}`,
  } = product;

  return (
    <div
      className={productCardVariants({ variant, aspectRatio, className })}
      {...props}
    >
      {/* Product Image */}
      <div className="relative overflow-hidden">
        <div className={`${aspectRatio === 'square' ? 'aspect-square' : aspectRatio === 'video' ? 'aspect-video' : aspectRatio === 'portrait' ? 'aspect-[3/4]' : 'aspect-[4/3]'}`}>
          <Image
            src={imageSrc}
            alt={name}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        
        {/* Badge if available */}
        {badge && (
          <div className="absolute top-3 right-3 bg-primary-500 text-white text-xs font-bold px-2 py-1 rounded-md">
            {badge}
          </div>
        )}
      </div>

      {/* Product Content */}
      <div className="p-4 sm:p-5">
        {showCategory && category && (
          <div className="text-xs font-medium text-primary-600 mb-2 uppercase tracking-wider">
            {category}
          </div>
        )}
        
        <h3 className="text-lg sm:text-xl font-bold text-earth-900 mb-2 group-hover:text-primary-600 transition-colors">
          <Link href={url} className="hover:underline">
            {name}
          </Link>
        </h3>
        
        {showDescription && (
          <p className="text-earth-700 text-sm mb-4 line-clamp-2">
            {description}
          </p>
        )}
        
        <div className="flex items-center justify-between mt-auto">
          {showPrice && price && (
            <div className="font-bold text-earth-900">{price}</div>
          )}
          
          {showButton && (
            <Button
              variant="outline"
              size="sm"
              href={url}
              className="ml-auto"
            >
              {buttonText}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
