"use client";

import React from 'react';
import Image from 'next/image';
import { cva, type VariantProps } from 'class-variance-authority';
import { Button } from '@/components/atoms/Button';

// Define hero banner variants
const heroBannerVariants = cva(
  "relative flex items-center justify-center overflow-hidden",
  {
    variants: {
      size: {
        sm: "min-h-[50vh]",
        md: "min-h-[70vh]",
        lg: "min-h-[90vh]",
        full: "min-h-screen",
      },
      contentPosition: {
        center: "text-center items-center justify-center",
        left: "text-left items-center justify-start",
        right: "text-right items-center justify-end",
      },
      textColor: {
        light: "text-white",
        dark: "text-earth-950",
      },
    },
    defaultVariants: {
      size: "lg",
      contentPosition: "center",
      textColor: "light",
    },
  }
);

export interface HeroBannerProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof heroBannerVariants> {
  title: string;
  subtitle?: string;
  imageSrc: string;
  imageAlt: string;
  primaryCTA?: {
    text: string;
    href: string;
  };
  secondaryCTA?: {
    text: string;
    href: string;
  };
  overlayColor?: string;
  withScrollIndicator?: boolean;
  highlightedText?: string;
}

export function HeroBanner({
  className,
  size,
  contentPosition,
  textColor,
  title,
  subtitle,
  imageSrc,
  imageAlt,
  primaryCTA,
  secondaryCTA,
  overlayColor = "from-primary-950/70 to-earth-950/90",
  withScrollIndicator = true,
  highlightedText,
  ...props
}: HeroBannerProps) {
  // Replace highlighted text with span if provided
  const formattedTitle = highlightedText
    ? title.replace(
        highlightedText,
        `<span class="text-primary-400">${highlightedText}</span>`
      )
    : title;

  return (
    <section
      className={heroBannerVariants({ size, contentPosition, textColor, className })}
      {...props}
    >
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src={imageSrc}
          alt={imageAlt}
          fill
          priority
          className="object-cover"
          sizes="100vw"
        />
        <div className={`absolute inset-0 bg-gradient-to-b ${overlayColor}`}></div>
        <div className="absolute inset-0 bg-black/40"></div> {/* Additional overlay for better text visibility */}
        <div className="bg-texture"></div>
      </div>

      {/* Content */}
      <div className="container-custom relative z-10 mt-16">
        <div className={`max-w-3xl mx-auto ${contentPosition === 'center' ? 'text-center' : contentPosition === 'right' ? 'ml-auto text-right' : ''}`}>
          <h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fade-in-up"
            dangerouslySetInnerHTML={{ __html: formattedTitle }}
          />

          {subtitle && (
            <p
              className="text-xl md:text-2xl opacity-90 mb-8 animate-fade-in-up"
              style={{ animationDelay: "200ms" }}
            >
              {subtitle}
            </p>
          )}

          {(primaryCTA || secondaryCTA) && (
            <div
              className={`flex flex-col sm:flex-row gap-4 ${contentPosition === 'center' ? 'justify-center' : contentPosition === 'right' ? 'justify-end' : 'justify-start'} animate-fade-in-up`}
              style={{ animationDelay: "400ms" }}
            >
              {primaryCTA && (
                <Button
                  variant="primary"
                  size="lg"
                  href={primaryCTA.href}
                  className="hover-bloom"
                >
                  {primaryCTA.text}
                </Button>
              )}

              {secondaryCTA && (
                <Button
                  variant="outline"
                  size="lg"
                  href={secondaryCTA.href}
                  className={textColor === 'light' ? 'border-white text-white hover:bg-white/10' : ''}
                >
                  {secondaryCTA.text}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Scroll Indicator */}
      {withScrollIndicator && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-8 h-12 rounded-full border-2 border-white/30 flex items-center justify-center">
            <div className="w-1.5 h-3 bg-white/50 rounded-full animate-pulse-slow"></div>
          </div>
        </div>
      )}
    </section>
  );
}
