"use client";

import { useState, useEffect, JSX } from "react";
import Link from "next/link";
import Image from "next/image";
import React from "react";
import { usePathname } from "next/navigation";
import QuoteRequestModal from "@/components/molecules/QuoteRequestModal";

/**
 * Header component with responsive navigation and scroll effects
 *
 * Features:
 * - Responsive mobile menu with animations
 * - Scroll-aware styling (transparent to solid background)
 * - Animated menu transitions
 * - Accessible navigation
 * - Glassmorphism design
 * - Single large leaf background for active items
 */
export default function Header(): React.ReactNode {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const pathname = usePathname();

  // Handle scroll events to change header appearance
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);

    // Clean up event listener
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);

    // Prevent body scroll when menu is open
    if (!isMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  };

// LeavesBackground component with single large leaf
function LeavesBackground(): JSX.Element {
  return (
    <Image src='/leaf-svgrepo-com.svg' alt="" width={50} height={50} className="rotate-50" />
  );
}


  return (
    <header
      id="navigation"
      role="banner"
      className={`fixed top-0 w-full z-50 transition-all duration-300 text-white font-baru-regularbold ${
        scrolled
          ? "bg-[#ffbc00]  shadow-3xl  py-3"
          : "py-2 text-yellow-400"
      }`}
      style={{
        background: scrolled
          ? "#ffbc00"
          : "",
      }}
    >
      <div className="container-custom">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link
            href="/"
            className="relative z-10 flex items-centergroup"
          >
            {scrolled?
              <Image
                src="/assets/White.png"
                alt="AfricSource Logo"
                width={200}
                height={100}
                className="object-cover transition-transform duration-500 group-hover:scale-110"
                onError={(e) => {
                  e.currentTarget.src = "/globe.svg";
                }}
              />
              :
            <Image
                src="/assets/Harvest Green_.png"
                alt="AfricSource Logo"
                width={200}
                height={100}
                className="object-cover transition-transform duration-500 group-hover:scale-110"
                onError={(e) => {
                  e.currentTarget.src = "/globe.svg";
                }}
              />}
          </Link>

          {/* Desktop Navigation */}
          <nav
            className="hidden md:flex items-center space-x-8"
            role="navigation"
            aria-label="Main navigation"
          >
            {[
              {
                name: "Home",
                href: "/",
              },
              {
                name: "About",
                href: "/about",
              },
              {
                name: "Products",
                href: "/products",
              },
              {
                name: "Sustainability",
                href: "/sustainability",
              },
              {
                name: "Contact",
                href: "/contact",
              },
            ].map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative font-medium px-4 py-2 rounded-lg transition-all duration-300 hover:text-forest-green group
                    ${scrolled ? "text-earth-800" : "text-amber-300 "}
                    ${
                      isActive
                        ? "text-green-200 font-bold scale-105"
                        : "hover:scale-105 "
                    }
                    transition-transform duration-300`}
                  style={{
                    transitionProperty:
                      "color, background, box-shadow, transform",
                  }}
                >
                  {/* Single large leaf background for active and hover states */}
                  <div className={`absolute inset-0 text-forest-green transition-opacity duration-300 ${
                    isActive ? "opacity-100" : "opacity-0 group-hover:opacity-50"
                  }`}>
                    <LeavesBackground />
                  </div>
                  <span className="relative z-10">{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:block">
            <button
              onClick={() => setIsQuoteModalOpen(true)}
              className="group px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-semibold rounded-full shadow-2xl shadow-yellow-400/40 transition-all duration-300 transform hover:scale-105 hover:shadow-3xl backdrop-blur-sm"
            >
              Request a Quote
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className={`md:hidden relative z-10 p-2 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 ${
              scrolled ? "text-white" : "text-white"
            }`}
            onClick={toggleMenu}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span
                className={`block w-5 h-0.5 transition-all duration-300 ease-out ${
                  isMenuOpen
                    ? "rotate-45 translate-y-0.5 bg-white"
                    : "bg-white"
                }`}
              ></span>
              <span
                className={`block w-5 h-0.5 mt-1 transition-all duration-300 ease-out ${
                  isMenuOpen
                    ? "-rotate-45 -translate-y-1 bg-white"
                    : "bg-white"
                }`}
              ></span>
            </div>
          </button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      <div
        id="mobile-menu"
        className={`fixed inset-0 z-0 transition-all duration-500 ${
          isMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
        }`}
        style={{
          background: "linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 188, 0, 0.1) 100%)",
          backdropFilter: "blur(20px)",
        }}
        aria-hidden={!isMenuOpen}
      >
        <nav
          className="flex flex-col space-y-6 items-center justify-center h-full text-center py-8"
          role="navigation"
          aria-label="Mobile navigation"
        >
          {[
            {
              name: "Home",
              href: "/",
            },
            {
              name: "About",
              href: "/about",
            },
            {
              name: "Products",
              href: "/products",
            },
            {
              name: "Sustainability",
              href: "/sustainability",
            },
            {
              name: "Contact",
              href: "/contact",
            },
          ].map((item, index) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`text-2xl font-medium relative px-6 py-3 rounded-lg transition-all
                  transform text-earthy-cocoa hover:text-forest-green group ${
                    isMenuOpen
                      ? "translate-y-0 opacity-100"
                      : "translate-y-4 opacity-0"
                  }
                  ${
                    isActive
                      ? "scale-110 font-bold text-forest-green"
                      : "hover:scale-105"
                  }
                  transition-transform duration-300`}
                style={{
                  transitionDelay: `${150 + index * 50}ms`,
                  transitionProperty:
                    "color, background, box-shadow, transform",
                }}
                onClick={toggleMenu}
              >
                {/* Single large leaf background for active and hover states in mobile */}
                <div className={`absolute inset-0 text-forest-green transition-opacity duration-300 ${
                  isActive ? "opacity-100" : "opacity-0 group-hover:opacity-70"
                }`}>
                  <LeavesBackground />
                </div>
                <span className="relative z-10">{item.name}</span>
              </Link>
            );
          })}
          <button
            onClick={() => {
              setIsQuoteModalOpen(true);
              toggleMenu();
            }}
            className="group px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-3xl "
          >
            Request a Quote
          </button>
        </nav>
      </div>

      {/* Quote Request Modal */}
      <QuoteRequestModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
      />
    </header>
  );
}