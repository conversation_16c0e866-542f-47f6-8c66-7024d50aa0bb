"use client";

import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { FocusManager, AriaUtils, KeyboardNavigation } from '@/lib/accessibility';

interface AccessibleModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const AccessibleModal: React.FC<AccessibleModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className = '',
  size = 'md'
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const titleId = useRef(AriaUtils.generateId('modal-title'));
  const descriptionId = useRef(AriaUtils.generateId('modal-description'));

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus the modal
      setTimeout(() => {
        modalRef.current?.focus();
      }, 100);

      // Trap focus within the modal
      const cleanup = modalRef.current ? FocusManager.trapFocus(modalRef.current) : null;

      // Prevent body scroll
      document.body.style.overflow = 'hidden';

      // Announce modal opening to screen readers
      AriaUtils.announceToScreenReader(`${title} dialog opened`, 'assertive');

      return () => {
        cleanup?.();
        document.body.style.overflow = '';
      };
    } else {
      // Restore focus to the previously focused element
      FocusManager.restoreFocus(previousFocusRef.current);
    }
  }, [isOpen, title]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === KeyboardNavigation.KEYS.ESCAPE) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        // Close modal when clicking on backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
      role="dialog"
      aria-modal="true"
      aria-labelledby={titleId.current}
      aria-describedby={descriptionId.current}
    >
      <div
        ref={modalRef}
        className={`
          bg-white rounded-lg shadow-xl w-full ${sizeClasses[size]} 
          max-h-[90vh] overflow-y-auto focus:outline-none
          ${className}
        `}
        tabIndex={-1}
        role="document"
      >
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 
            id={titleId.current}
            className="text-xl font-semibold text-gray-900"
          >
            {title}
          </h2>
          <button
            onClick={onClose}
            className="
              text-gray-400 hover:text-gray-600 focus:outline-none 
              focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 
              rounded-md p-1
            "
            aria-label="Close dialog"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Modal Content */}
        <div 
          id={descriptionId.current}
          className="p-6"
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default AccessibleModal;
