"use client";

import React, { FC, ReactNode } from "react";
import Image from "next/image";

interface HeroProps {
  title: ReactNode;
  description: ReactNode;
  backgroundSrc: string;
  primaryButtonText: string;
  primaryButtonHref: string;
  secondaryButtonText: string;
  secondaryButtonHref: string;
}

const ModernHeroWithBackground: FC<HeroProps> = ({
  title,
  description,
  primaryButtonText,
  primaryButtonHref,
  secondaryButtonText,
  secondaryButtonHref,
  backgroundSrc,
}) => (
  <div className="relative pt-20 min-h-[80vh] flex items-center justify-center bg-deep-charcoal text-white overflow-hidden rounded-bl-[150px]">
    {/* Background Image */}
    <div className="absolute inset-0 -z-10">
      <Image
        src={backgroundSrc}
        alt="Hero background"
        fill
        style={{ objectFit: "cover", objectPosition: "center" }}
        priority
        quality={80}
      />
      <div className="absolute inset-0 bg-black opacity-30" /> {/* Optional overlay to darken */}
    </div>

    {/* Decorative Bubbles */}
    <div className="absolute inset-0 z-10 pointer-events-none">
      <div className="absolute top-20 left-12 w-32 h-32 bg-sun-gold opacity-20 rounded-full blur-xl" />
      <div className="absolute bottom-28 right-24 w-24 h-24 bg-forest-green opacity-15 rounded-full blur-lg" />
      <div className="absolute top-1/2 left-1/4 w-20 h-20 bg-white opacity-10 rounded-full blur-md" />
    </div>

    {/* Content */}
    <div className="relative z-20 text-center max-w-4xl mx-auto px-6 py-20">
      <span className="inline-block px-4 py-2 rounded-full text-sm font-baru-semibold text-forest-green bg-sun-gold/20 border border-sun-gold mb-6 shadow-sm tracking-wider uppercase">
        🌍 AFRICAN EXPORT EXCELLENCE
      </span>

      <h1 className="text-4xl md:text-6xl font-baru-bold mb-6 leading-tight tracking-tight text-sun-gold drop-shadow-xl hover:scale-105 transition-transform duration-300">
        {title}
      </h1>

      <p className="text-lg md:text-2xl mb-10 text-white leading-relaxed">
        {description}
      </p>

      <div className="flex flex-col sm:flex-row gap-6 justify-center">
        <a
          href={primaryButtonHref}
          className="bg-forest-green hover:bg-forest-green/90 text-white px-8 py-4 rounded-xl font-baru-semibold text-base shadow-lg transition-all duration-300 transform hover:-translate-y-1"
        >
          {primaryButtonText}
        </a>
        <a
          href={secondaryButtonHref}
          className="border-2 border-sun-gold text-white hover:bg-sun-gold hover:text-deep-charcoal px-8 py-4 rounded-xl font-baru-semibold text-base transition-all duration-300"
        >
          {secondaryButtonText}
        </a>
      </div>
    </div>
  </div>
);

export default ModernHeroWithBackground;
