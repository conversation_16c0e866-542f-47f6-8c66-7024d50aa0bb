'use client';

import React from 'react';
import Image from 'next/image';

interface SEOImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  sizes?: string;
  fill?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  title?: string;
  caption?: string;
}

const SEOImage: React.FC<SEOImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  fill = false,
  quality = 85,
  loading = 'lazy',
  title,
  caption,
}) => {
  // Ensure alt text is descriptive and not empty
  const optimizedAlt = alt || 'AfricSource product image';
  
  // Generate title if not provided
  const imageTitle = title || optimizedAlt;

  const imageComponent = fill ? (
    <Image
      src={src}
      alt={optimizedAlt}
      fill
      className={className}
      priority={priority}
      sizes={sizes}
      quality={quality}
      title={imageTitle}
      loading={loading}
    />
  ) : (
    <Image
      src={src}
      alt={optimizedAlt}
      width={width || 400}
      height={height || 300}
      className={className}
      priority={priority}
      sizes={sizes}
      quality={quality}
      title={imageTitle}
      loading={loading}
    />
  );

  if (caption) {
    return (
      <figure className="relative">
        {imageComponent}
        <figcaption className="mt-2 text-sm text-gray-600 text-center">
          {caption}
        </figcaption>
      </figure>
    );
  }

  return imageComponent;
};

export default SEOImage;
