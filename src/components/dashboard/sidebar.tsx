import {
  X,
  LayoutDashboard,
  Package,
  ShoppingCart,
  Tag,
  Users,
  Settings,
  LogOut,
  MessageSquare,
} from 'lucide-react';
import Link from 'next/link';

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export default function Sidebar({ sidebarOpen, setSidebarOpen }: SidebarProps) {
  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: <LayoutDashboard /> },
    { name: 'Products', href: '/dashboard/products', icon: <Package /> },
    { name: 'Categories', href: '/dashboard/categories', icon: <ShoppingCart /> },
    { name: 'Tags', href: '/dashboard/tags', icon: <Tag /> },
    { name: 'Messages', href: '/dashboard/contacts', icon: <MessageSquare /> },
    { name: 'Settings', href: '/dashboard/settings', icon: <Settings /> },
  ];

  return (
    <div
      className={`fixed min-h-screen inset-y-0 left-0 z-50 w-64 bg-emerald-800 text-white transform transition-transform duration-300 ease-in-out lg:transform-none ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      }`}
    >
      {/* Close button for mobile */}
      <div className="flex items-center justify-between lg:hidden px-4 py-3 border-b border-white/20">
        <h1 className="text-lg font-bold">Admin Panel</h1>
        <button onClick={() => setSidebarOpen(false)}>
          <X className="w-6 h-6 text-white" />
        </button>
      </div>

      {/* Navigation */}
      <div className="flex flex-col gap-2 p-4">
        {navigation.map((item) => (
          <SidebarButton
            key={item.name}
            href={item.href}
            icon={item.icon}
            label={item.name}
          />
        ))}
      </div>

      {/* User Info */}
      <div className="absolute bottom-0 w-full p-4 border-t border-white/20">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full" >
            <Users/>
          </div>
          <div>
            <p className="text-sm font-semibold">Admin</p>
            <p className="text-xs text-white/70"><EMAIL></p>
          </div>
        </div>
        <button className="mt-3 flex items-center gap-2 text-sm hover:underline">
          <LogOut className="w-4 h-4" />
          Logout
        </button>
      </div>
    </div>
  );
}

interface SidebarButtonProps {
  href: string;
  icon: React.ReactNode;
  label: string;
}

function SidebarButton({ href, icon, label }: SidebarButtonProps) {
  return (
    <Link href={href} className="block">
      <div className="flex items-center gap-3 p-2 rounded hover:bg-emerald-700 transition">
        <span className="w-5 h-5">{icon}</span>
        <span>{label}</span>
      </div>
    </Link>
  );
}
