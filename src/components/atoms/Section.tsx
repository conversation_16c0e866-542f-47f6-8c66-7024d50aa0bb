"use client";

import React, { useEffect, useRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

// Define section variants using class-variance-authority
const sectionVariants = cva(
  "relative w-full",
  {
    variants: {
      variant: {
        default: "bg-white",
        primary: "bg-primary-50",
        secondary: "bg-secondary-50",
        earth: "bg-earth-50",
        dark: "bg-earth-950 text-white",
        gradient: "bg-gradient-to-b from-primary-50 to-white",
      },
      padding: {
        none: "",
        sm: "py-8",
        md: "py-12 md:py-16",
        lg: "py-16 md:py-24",
        xl: "py-24 md:py-32",
      },
      container: {
        none: "",
        sm: "container-narrow mx-auto px-4 sm:px-6",
        md: "container-custom mx-auto px-4 sm:px-6",
        lg: "max-w-7xl mx-auto px-4 sm:px-6",
        xl: "max-w-8xl mx-auto px-4 sm:px-6",
        full: "w-full",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "md",
      container: "md",
    },
  }
);

export interface SectionProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof sectionVariants> {
  as?: React.ElementType;
  withTexture?: boolean;
  animateOnScroll?: boolean;
  animationDelay?: number;
}

const Section = React.forwardRef<HTMLElement, SectionProps>(
  ({ 
    className, 
    children, 
    variant, 
    padding, 
    container, 
    as: Component = "section", 
    withTexture = false,
    animateOnScroll = false,
    animationDelay = 0,
    ...props 
  }, ref) => {
    const sectionRef = useRef<HTMLElement>(null);
    
    // Set up intersection observer for scroll animations
    useEffect(() => {
      if (!animateOnScroll) return;
      
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setTimeout(() => {
                entry.target.classList.add('opacity-100', 'translate-y-0');
                entry.target.classList.remove('opacity-0', 'translate-y-10');
              }, animationDelay);
              observer.unobserve(entry.target);
            }
          });
        },
        { threshold: 0.1 }
      );
      
      if (sectionRef.current) {
        observer.observe(sectionRef.current);
      }
      
      return () => {
        if (sectionRef.current) {
          observer.unobserve(sectionRef.current);
        }
      };
    }, [animateOnScroll, animationDelay]);
    
    return (
      <Component
        ref={sectionRef || ref}
        className={sectionVariants({ variant, padding, container, className })}
        {...props}
      >
        {withTexture && <div className="bg-texture"></div>}
        {children}
      </Component>
    );
  }
);

Section.displayName = "Section";

export { Section, sectionVariants };
