import React, { useState } from 'react';
import { Search, ChevronDown } from 'lucide-react';
import Image from 'next/image'; // <-- import Next.js Image

const ProductCatalog = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [sortBy, setSortBy] = useState('Default');
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    'All',
    'Grains & Seeds',
    'Nuts & Kernels', 
    'Oils & Fats',
    'Beans & Legumes',
    'Spices & Herbs'
  ];

  const products = [
    {
      id: 1,
      name: 'Sesame Seeds',
      category: 'Grains & Seeds',
      image: 'https://images.unsplash.com/photo-1589927986089-35812388d1f4?w=500&h=300&fit=crop',
      shortDescription: 'Premium quality, sourced from trusted African farmers.',
      fullDescription: 'Our golden, high-oil content sesame seeds are carefully harvested and processed to maintain maximum freshness and nutritional value. Perfect for oil extraction, baking, and direct consumption.',
      origin: 'West Africa',
      quality: '99.5% purity, 48-52% oil content',
      specifications: 'Moisture: <6%, FFA: <2%'
    },
    {
      id: 2,
      name: 'Cashew Nuts',
      category: 'Nuts & Kernels',
      image: 'https://images.unsplash.com/photo-1509465985-7bdc1c87a8e6?w=500&h=300&fit=crop',
      shortDescription: 'Processed and exported with strict quality controls.',
      fullDescription: 'Our premium cashew nuts are carefully selected, processed, and graded to meet international standards. Available in various grades for different market requirements.',
      origin: 'Côte d\'Ivoire, Ghana',
      quality: 'W180, W210, W240, W320, W450 grades available',
      specifications: 'Moisture: <5%, Broken: <5%'
    },
    {
      id: 3,
      name: 'Shea Butter',
      category: 'Oils & Fats',
      image: 'https://images.unsplash.com/photo-1596971091341-77cd6ad5b8b9?w=500&h=300&fit=crop',
      shortDescription: 'Ethically harvested, ideal for food and cosmetics industries.',
      fullDescription: 'Our unrefined shea butter is ethically sourced from women\'s cooperatives across West Africa. Known for its moisturizing properties and rich vitamin content, it\'s perfect for cosmetics and food applications.',
      origin: 'Ghana, Burkina Faso',
      quality: 'Grade A, unrefined, low free fatty acid content',
      specifications: 'FFA: <3%, Moisture: <0.05%'
    },
    {
      id: 4,
      name: 'Black Pepper',
      category: 'Spices & Herbs',
      image: 'https://images.unsplash.com/photo-1506368083636-319e5d5e5b44?w=500&h=300&fit=crop',
      shortDescription: 'Premium grade spice with intense flavor profile.',
      fullDescription: 'Our black pepper is sourced from the finest plantations, ensuring maximum piperine content and aromatic intensity. Perfect for culinary and industrial applications.',
      origin: 'Kerala, India',
      quality: 'ASTA 450+ piperine content',
      specifications: 'Moisture: <12%, Piperine: >7%'
    },
    {
      id: 5,
      name: 'Cocoa Beans',
      category: 'Beans & Legumes',
      image: 'https://images.unsplash.com/photo-1558618047-3c92c8c8fd4d?w=500&h=300&fit=crop',
      shortDescription: 'Premium fermented cocoa beans for chocolate production.',
      fullDescription: 'Our cocoa beans are carefully fermented and dried to achieve optimal flavor development. Sourced from sustainable farms with fair trade practices.',
      origin: 'Ghana, Côte d\'Ivoire',
      quality: 'Grade 1, well-fermented',
      specifications: 'Moisture: <7.5%, Bean count: 100g'
    },
    {
      id: 6,
      name: 'Palm Oil',
      category: 'Oils & Fats',
      image: 'https://images.unsplash.com/photo-1605027990121-3b2c6c8cb9ea?w=500&h=300&fit=crop',
      shortDescription: 'Sustainably sourced crude palm oil.',
      fullDescription: 'Our crude palm oil is extracted from fresh palm fruits using sustainable methods. Rich in vitamins and suitable for various industrial applications.',
      origin: 'Nigeria, Malaysia',
      quality: 'RSPO certified, high carotene content',
      specifications: 'FFA: <5%, Moisture: <0.25%'
    }
  ];

  const filteredProducts = products.filter(product => {
    const matchesCategory = activeCategory === 'All' || product.category === activeCategory;
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.shortDescription.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-6">
          {/* Search and Sort Bar */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
            <div className="relative flex-1 max-w-lg">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center gap-3">
              <span className="text-gray-700 font-medium">Sort by:</span>
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option>Default</option>
                  <option>Name A-Z</option>
                  <option>Name Z-A</option>
                  <option>Category</option>
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
              </div>
            </div>
          </div>

          {/* Category Navigation */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                  activeCategory === category
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Showing {filteredProducts.length} products
            {activeCategory !== 'All' && ` in ${activeCategory}`}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden"
            >
              {/* Product Image */}
              <div className="relative w-full h-64">
                <Image
                  src={product.image}
                  alt={product.name}
                  layout="fill"
                  objectFit="cover"
                  className="rounded-t-xl"
                  priority={true}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                />
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                  {product.category}
                </div>
              </div>

              {/* Product Content */}
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {product.name}
                </h3>
                
                <p className="text-gray-600 mb-4 font-medium">
                  {product.shortDescription}
                </p>
                
                <p className="text-gray-700 mb-6 leading-relaxed">
                  {product.fullDescription}
                </p>

                {/* Product Details */}
                <div className="space-y-3 text-sm">
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-20">Origin:</span>
                    <span className="text-gray-700">{product.origin}</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-20">Quality:</span>
                    <span className="text-gray-700">{product.quality}</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-20">Specs:</span>
                    <span className="text-gray-700">{product.specifications}</span>
                  </div>
                </div>

                {/* Action Button */}
                <button className="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                  Request Quote
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-16">
            <div className="text-gray-400 text-6xl mb-4">📦</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductCatalog;