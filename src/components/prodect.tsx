"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Heart,
  ShoppingCart,
  Star,
  Truck,
  Shield,
  RotateCcw,
  Share2,
  Minus,
  Plus,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

const ProductPage = () => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState("M");
  const [selectedColor, setSelectedColor] = useState("Blue");
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [activeTab, setActiveTab] = useState("description");

  const product = {
    name: "Premium Wireless Headphones",
    brand: "AudioTech Pro",
    price: 199.99,
    originalPrice: 249.99,
    rating: 4.8,
    reviews: 2847,
    inStock: true,
    description:
      "Experience crystal-clear audio with our premium wireless headphones featuring active noise cancellation, 30-hour battery life, and comfortable over-ear design.",
    features: [
      "Active Noise Cancellation",
      "30-hour battery life",
      "Bluetooth 5.0 connectivity",
      "Quick charge: 10 min = 3 hours playback",
      "Premium leather ear cushions",
      "Built-in microphone",
    ],
    sizes: ["S", "M", "L", "XL"],
    colors: ["Blue", "Black", "White", "Red"],
    images: [
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-1487215078519-e21cc028cb29?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=500&fit=crop",
    ],
  };

  const relatedProducts = [
    {
      id: 1,
      name: "Wireless Earbuds",
      price: 89.99,
      image:
        "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=200&h=200&fit=crop",
    },
    {
      id: 2,
      name: "Bluetooth Speaker",
      price: 129.99,
      image:
        "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=200&h=200&fit=crop",
    },
    {
      id: 3,
      name: "Phone Stand",
      price: 24.99,
      image:
        "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=200&h=200&fit=crop",
    },
    {
      id: 4,
      name: "USB-C Cable",
      price: 19.99,
      image:
        "https://images.unsplash.com/photo-1558618047-3c92c8c8fd4d?w=200&h=200&fit=crop",
    },
  ];

  const handleQuantityChange = (change: number) => {
    setQuantity(Math.max(1, quantity + change));
  };

  const nextImage = () => {
    setSelectedImage((prev) => (prev + 1) % product.images.length);
  };

  const prevImage = () => {
    setSelectedImage((prev) => (prev - 1 + product.images.length) % product.images.length);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8 text-sm text-gray-600" aria-label="Breadcrumb">
        <ol className="list-reset flex space-x-2">
          <li>Home</li>
          <li>/</li>
          <li>Electronics</li>
          <li>/</li>
          <li>Audio</li>
          <li>/</li>
          <li aria-current="page" className="text-gray-900 font-semibold">
            {product.name}
          </li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
        {/* Product Images */}
        <div className="space-y-4">
          <div className="relative group">
            <div className="relative w-full h-[24rem] lg:h-[500px] rounded-lg overflow-hidden">
              <Image
                src={product.images[selectedImage]}
                alt={`${product.name} image ${selectedImage + 1}`}
                fill
                sizes="(min-width: 1024px) 500px, 100vw"
                style={{ objectFit: "cover" }}
                priority
              />
            </div>
            <button
              onClick={prevImage}
              aria-label="Previous Image"
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button
              onClick={nextImage}
              aria-label="Next Image"
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>

          <div className="flex space-x-2 overflow-x-auto">
            {product.images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                aria-label={`View image ${index + 1}`}
                className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                  selectedImage === index ? "border-blue-500" : "border-gray-200"
                }`}
              >
                <div className="relative w-full h-full">
                  <Image
                    src={image}
                    alt={`${product.name} thumbnail ${index + 1}`}
                    fill
                    sizes="80px"
                    style={{ objectFit: "cover" }}
                  />
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <p className="text-blue-600 font-medium mb-2">{product.brand}</p>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>

            <div className="flex items-center space-x-4 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating) ? "text-yellow-400 fill-current" : "text-gray-300"
                    }`}
                  />
                ))}
                <span className="ml-2 text-sm text-gray-600">
                  {product.rating} ({product.reviews} reviews)
                </span>
              </div>
            </div>

            <div className="flex items-baseline space-x-3 mb-6">
              <span className="text-3xl font-bold text-gray-900">${product.price.toFixed(2)}</span>
              <span className="text-xl text-gray-500 line-through">${product.originalPrice.toFixed(2)}</span>
              <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                Save {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
              </span>
            </div>
          </div>

          {/* Color Selection */}
          <div>
            <h3 className="font-medium mb-3">Color: {selectedColor}</h3>
            <div className="flex space-x-3">
              {product.colors.map((color) => (
                <button
                  key={color}
                  onClick={() => setSelectedColor(color)}
                  aria-pressed={selectedColor === color}
                  className={`w-12 h-12 rounded-full border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    selectedColor === color ? "border-blue-500" : "border-gray-300"
                  }`}
                  style={{
                    backgroundColor: color.toLowerCase() === "white" ? "#ffffff" : color.toLowerCase(),
                  }}
                />
              ))}
            </div>
          </div>

          {/* Size Selection */}
          <div>
            <h3 className="font-medium mb-3">Size: {selectedSize}</h3>
            <div className="flex space-x-2">
              {product.sizes.map((size) => (
                <button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  aria-pressed={selectedSize === size}
                  className={`px-4 py-2 border rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    selectedSize === size
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
          </div>

          {/* Quantity */}
          <div>
            <h3 className="font-medium mb-3">Quantity</h3>
            <div className="flex items-center space-x-3">
              <div className="flex items-center border rounded-lg">
                <button
                  onClick={() => handleQuantityChange(-1)}
                  className="p-2 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Decrease quantity"
                >
                  <Minus className="w-4 h-4" />
                </button>
                <span className="px-4 py-2 border-x" aria-live="polite" aria-atomic="true">
                  {quantity}
                </span>
                <button
                  onClick={() => handleQuantityChange(1)}
                  className="p-2 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Increase quantity"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              <span
                className={`font-medium ${
                  product.inStock ? "text-green-600" : "text-red-600"
                }`}
              >
                {product.inStock ? "In Stock" : "Out of Stock"}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <button
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg flex items-center justify-center space-x-2 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Add to cart"
            >
              <ShoppingCart className="w-5 h-5" />
              <span>Add to Cart</span>
            </button>

            <div className="flex space-x-3">
              <button
                onClick={() => setIsWishlisted(!isWishlisted)}
                aria-pressed={isWishlisted}
                className={`flex-1 border-2 font-medium py-3 px-6 rounded-lg flex items-center justify-center space-x-2 transition-colors focus:outline-none focus:ring-2 ${
                  isWishlisted
                    ? "border-red-500 text-red-600 bg-red-50 focus:ring-red-500"
                    : "border-gray-300 text-gray-700 hover:border-gray-400 focus:ring-blue-500"
                }`}
              >
                <Heart className={`w-5 h-5 ${isWishlisted ? "fill-current" : ""}`} />
                <span>Wishlist</span>
              </button>

              <button
                className="flex-1 border-2 border-gray-300 text-gray-700 hover:border-gray-400 font-medium py-3 px-6 rounded-lg flex items-center justify-center space-x-2 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Share product"
              >
                <Share2 className="w-5 h-5" />
                <span>Share</span>
              </button>
            </div>
          </div>

          {/* Features */}
          <div className="flex flex-wrap gap-4 pt-6 border-t text-gray-600 text-sm">
            <div className="flex items-center space-x-2">
              <Truck className="w-5 h-5" />
              <span>Free shipping</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>2-year warranty</span>
            </div>
            <div className="flex items-center space-x-2">
              <RotateCcw className="w-5 h-5" />
              <span>30-day returns</span>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <section className="mb-16" aria-label="Product details tabs">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8" role="tablist">
            {["description", "specifications", "reviews"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                role="tab"
                aria-selected={activeTab === tab}
                aria-controls={`tab-panel-${tab}`}
                id={`tab-${tab}`}
                className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        <div
          id={`tab-panel-${activeTab}`}
          role="tabpanel"
          aria-labelledby={`tab-${activeTab}`}
          className="py-8"
        >
          {activeTab === "description" && (
            <div className="prose max-w-none">
              <p className="text-gray-700 mb-6">{product.description}</p>
              <h3 className="text-lg font-semibold mb-4">Features</h3>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {activeTab === "specifications" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">Technical Specs</h4>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="text-gray-600">Connectivity</dt>
                    <dd>Bluetooth 5.0</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-600">Battery Life</dt>
                    <dd>30 hours</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-600">Weight</dt>
                    <dd>250g</dd>
                  </div>
                </dl>
              </div>
            </div>
          )}

          {activeTab === "reviews" && (
            <div>
              <p className="text-gray-600">Customer reviews coming soon...</p>
            </div>
          )}
        </div>
      </section>

      {/* Related Products */}
      <section aria-label="Related products">
        <h2 className="text-2xl font-bold mb-8">You might also like</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {relatedProducts.map((product) => (
            <div key={product.id} className="group cursor-pointer">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-3 relative">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  sizes="(min-width: 768px) 200px, 50vw"
                  className="group-hover:scale-105 transition-transform duration-300 object-cover"
                />
              </div>
              <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                {product.name}
              </h3>
              <p className="text-gray-600">${product.price.toFixed(2)}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default ProductPage;
