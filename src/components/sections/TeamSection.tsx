"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { User, MapPin, Mail, Linkedin, Globe, ChevronDown, ChevronUp } from 'lucide-react'; // Added Chevron icons
import teamData from '@/data/team.json';

interface TeamMember {
  id: string;
  name: string;
  position: string;
  department: string;
  bio: string;
  image: string;
  email: string;
  linkedin?: string;
  specialties: string[];
  location: string;
  languages: string[];
}

interface TeamSectionProps {
  showTitle?: boolean;
  maxMembers?: number;
  className?: string;
}

export default function TeamSection({ 
  showTitle = true, 
  maxMembers,
  className = ""
}: TeamSectionProps) {
  const team: TeamMember[] = teamData.team;
  const displayedTeam = maxMembers ? team.slice(0, maxMembers) : team;

  // Define a threshold for when to show "Read More"
  const BIO_LENGTH_THRESHOLD = 200; // Adjust as needed

  return (
    <section className={`py-16 px-6 md:px-24 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="max-w-7xl mx-auto">
        {showTitle && (
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full mb-6">
              <User className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Meet Our <span className="text-emerald-600">Team</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our diverse team of experts brings together decades of experience in international trade, 
              quality assurance, and sustainable agriculture to deliver excellence in every partnership.
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 justify-items-center gap-8">
          {displayedTeam.map((member) => (
            <TeamMemberCard key={member.id} member={member} bioLengthThreshold={BIO_LENGTH_THRESHOLD} />
          ))}
        </div>

        {maxMembers && team.length > maxMembers && (
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-6">
              Meet all {team.length} members of our dedicated team
            </p>
            <a
              href="/about#team"
              className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white font-semibold rounded-full hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105"
            >
              View Full Team
            </a>
          </div>
        )}
      </div>
    </section>
  );
}

// New component for individual team member card to encapsulate its state
interface TeamMemberCardProps {
  member: TeamMember;
  bioLengthThreshold: number;
}

const TeamMemberCard: React.FC<TeamMemberCardProps> = ({ member, bioLengthThreshold }) => {
  const [showFullBio, setShowFullBio] = useState(false);
  const needsReadMore = member.bio.length > bioLengthThreshold;

  return (
    <div
      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
    >
      {/* Member Image */}
      <div className="relative h-100 bg-gradient-to-br from-emerald-100 to-orange-100 overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
            {/* If you have actual member images, replace this div with the Image component: */}
            <Image
              src={member.image} // Assuming member.image holds the path
              alt={member.name}
              width={505}
              height={571}
              // sizes="(max-width: 768px) 100vw, 300px"
              style={{ objectFit: 'contain' }}
              className="rounded-full" // Adjust based on your design
            />    
          {/* <div className="w-32 h-32 bg-gradient-to-br from-emerald-500 to-orange-500 rounded-full flex items-center justify-center">
            <User className="w-16 h-16 text-white" />
          </div> */}
        </div>
        {/* Overlay for future image implementation */}
        <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300"></div>
      </div>

      {/* Member Info */}
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-xl font-bold text-gray-900 mb-1">
            {member.name}
          </h3>
          <p className="text-emerald-600 font-semibold mb-1">
            {member.position}
          </p>
          <p className="text-sm text-gray-500 mb-3">
            {member.department}
          </p>
        </div>

        {/* Bio with conditional clamping and read more/less */}
        <p className={`text-gray-600 text-sm leading-relaxed mb-4 ${!showFullBio && needsReadMore ? 'line-clamp-3' : ''}`}>
          {member.bio}
        </p>
        
        {needsReadMore && (
          <button
            onClick={() => setShowFullBio(!showFullBio)}
            className="flex items-center text-emerald-600 hover:text-emerald-700 text-sm font-semibold transition-colors duration-200 mb-4"
          >
            {showFullBio ? (
              <>
                Show Less <ChevronUp className="ml-1 w-4 h-4" />
              </>
            ) : (
              <>
                Read More <ChevronDown className="ml-1 w-4 h-4" />
              </>
            )}
          </button>
        )}

        {/* Location */}
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <MapPin className="w-4 h-4 mr-2 text-emerald-500" />
          {member.location}
        </div>

        {/* Specialties */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {member.specialties.slice(0, 2).map((specialty, idx) => (
              <span
                key={idx}
                className="px-3 py-1 bg-emerald-100 text-emerald-700 text-xs rounded-full font-medium"
              >
                {specialty}
              </span>
            ))}
            {member.specialties.length > 2 && (
              <span className="px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full font-medium">
                +{member.specialties.length - 2} more
              </span>
            )}
          </div>
        </div>

        {/* Languages */}
        <div className="mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <Globe className="w-4 h-4 mr-2 text-orange-500" />
            <span>{member.languages.join(', ')}</span>
          </div>
        </div>

        {/* Contact Links */}
        <div className="flex items-center space-x-3 pt-4 border-t border-gray-100">
          <a
            href={`mailto:${member.email}`}
            className="flex items-center justify-center w-10 h-10 bg-gray-100 hover:bg-emerald-500 text-gray-600 hover:text-white rounded-full transition-all duration-300"
            aria-label={`Email ${member.name}`}
          >
            <Mail className="w-4 h-4" />
          </a>
          {member.linkedin && (
            <a
              href={member.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-10 h-10 bg-gray-100 hover:bg-blue-600 text-gray-600 hover:text-white rounded-full transition-all duration-300"
              aria-label={`${member.name} on LinkedIn`}
            >
              <Linkedin className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>
    </div>
  );
};