import React from 'react';
import Image from 'next/image';
import { highlights } from '@/store'; // Assuming highlights is an array of objects

export default function CompanyHighlights() {
  return (
    // Main section with extended height for the airplane image and wavy bottom
    <section className="relative w-full">
      {/* Background Image Container */}
      <div className="absolute inset-0">
        <Image
          src="/assets/global-shipping-logistics.png"
          alt="Global Shipping Logistics Background with Airplane"
          fill
          className="object-cover object-top"
          priority
          sizes="100vw"
        />

        {/* Gradient Overlay for Readability */}
        <div className="absolute inset-0 bg-gradient-to-b
                        from-transparent via-black/30 to-black/80
                        sm:from-transparent sm:via-black/40 sm:to-black/90
                        lg:from-transparent lg:via-black/50 lg:to-black"
        />
      </div>

      {/* Content Grid */}
      {/* Padding adjusted to make space for the image "popping out" */}
      <div className="relative z-10 max-w-7xl mx-auto
                      grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4
                      gap-6 px-4
                      pt-24 sm:pt-32 lg:pt-56 pb-8 sm:pb-12 lg:pb-16">

        {highlights.map(({ title, description, icon: Icon }) => (
          // === Highlight Card - Integrated Template with new styles ===
          <div
            key={title}
            className="group relative cursor-pointer overflow-hidden
                       max-w-xs sm:max-w-sm mx-auto
                       px-4 pt-8 pb-6
                       rounded-lg shadow-xl ring-1 ring-gray-900/5
                       bg-gradient-to-r from-sun-gold/30 to-earthy-cocoa/30
                       opacity-70 transition-all duration-30
                       hover:-translate-y-1 hover:shadow-2xl group-hover:opacity-100"
          >
            {/* Expanding Circle Background */}
            <span
              className="absolute top-8 z-0 h-16 w-16 rounded-full
                         bg-earthy-cocoa/50 transition-all duration-300
                         group-hover:scale-[10] group-hover:bg-earthy-cocoa"
            ></span>

            {/* Card Content Wrapper */}
            <div className="relative z-10 mx-auto max-w-full"> {/* max-w-full to fit within card */}
              {/* Icon Circle */}
              <span
                className="grid h-16 w-16 place-items-center rounded-full
                           bg-earthy-cocoa/70 transition-all duration-300
                           group-hover:bg-sun-gold" // Changes to sun-gold on hover
              >
                {/* Your highlight Icon - size and color adapted */}
                <Icon size={32} className="text-white transition-all duration-300 group-hover:text-deep-charcoal" />
              </span>

              {/* Title */}
              <div className="pt-4 text-base font-semibold leading-7"> {/* Reduced padding */}
                <h5 className="text-white text-[18px] font-baru-semibold transition-all duration-300 group-hover:text-white">
                  {title}
                </h5>
              </div>

              {/* Description */}
              <div className="space-y-4 pt-4 text-sm leading-7 text-white transition-all duration-300 group-hover:text-white"> {/* Reduced padding, text-white for readability */}
                <p>{description}</p>
              </div>

            </div>
          </div>
          // === End Highlight Card ===
        ))}
      </div>
    </section>
  );
}