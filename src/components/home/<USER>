import React from "react";

import Image from "next/image";
import { Check } from "lucide-react";

export default function AboutSection () {

  const Impact = [
    "Premium quality agricultural exports",
    "Direct partnerships with local farmers",
    "Sustainable farming practices",
    "Global logistics and distribution",
    "Quality assurance and certification",
  ];

  return (
    <div className="min-h-screen bg-white p-6">
      <div className="max-w-8xl mx-auto">
        <div className="grid lg:grid-cols-1 gap-8 items-stretch justify-center">
          <div className="flex justify-center">
            <div
              className="relative p-8 lg:p-12 rounded-3xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col h-full max-w-4xl w-full overflow-hidden"
            >
              {/* Faint background gradient + image */}
              <div className="absolute inset-0 bg-gradient-to-r from-sun-gold to-forest-green opacity-10 rounded-3xl z-0">
               <div className="absolute inset-0 bg-gradient-to-r from-sun-gold to-forest-green opacity-10 rounded-3xl z-0">
  <Image
    src="/assets/Earthy Cocoa_.png"
    alt="Earthy Cocoa"
    fill
    className="object-cover mix-blend-overlay"
    sizes="100vw"
    priority
  />
</div>

              </div>

              <div className="relative z-10 flex-1 flex flex-col">
                {/* Section header */}
                <div className="mb-8">
                  <p className="font-baru-semibold mb-4 tracking-wide text-sm uppercase text-forest-green">
                    Our Impact
                  </p>
                  <div className="w-16 h-1 bg-forest-green rounded-full mb-6" />
                </div>

                <div className="space-y-6 flex-1">
                 <h4 className="text-xl font-baru-bold text-deep-charcoal mb-6 leading-relaxed">
  {`By choosing AfricSource, you're not just buying premium produce – you're supporting:`}
</h4>

                  {/* Impact List */}
                  <div className="space-y-4">
                    {Impact.map((impact, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-4 group"
                      >
                        <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-earthy-cocoa text-white group-hover:scale-110 transition-transform duration-300">
                          <Check className="w-5 h-5 font-bold" />
                        </div>
                        <p className="font-baru-regular text-base text-deep-charcoal leading-relaxed pt-1">
                          {impact}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <div className="mt-8 pt-6">
                  <button className="w-full py-4 px-6 rounded-2xl font-baru-semibold text-white bg-forest-green border-2 border-forest-green hover:scale-105 hover:shadow-lg transition-all duration-300">
                    Learn More About Our Mission
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};