import React from 'react'
import Image from "next/image";
import Link from "next/link";
import { AboutSectionData } from "@/types/types";

export default function WhySection({ data }: { data: AboutSectionData }) {
  return (
     <section
      className={`bg-white text-deep-charcoal w-full mt-20 overflow-hidden py-5 text-justify sm:px-3 sm:py-12 lg:py-16`}
    >
      <div className="max-w-7xl mx-auto">
        {/* Title */}
        <h2
          className="text-2xl sm:text-3xl lg:text-4xl font-baru-bold text-earthy-cocoa mb-6 sm:mb-8 lg:mb-12 text-center"
        >
          {data.title}
        </h2>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-12">

          {/* Features Section (will be on top on small screens, left on large screens) */}
          <div className="flex-1 flex flex-col justify-center space-y-4 sm:space-y-6 font-baru-regularbold px-5">
            {data.features.map((item, index) => (
              <div
                key={index}
                className="border-l-4 border-sun-gold pl-4 sm:pl-6"
              >
                <h6 className="text-lg sm:text-xl font-baru-semibold mb-2 sm:mb-3 text-earthy-cocoa">
                  {item.title}
                </h6>
                <p className="text-sm sm:text-base font-baru-regular text-deep-charcoal leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}

            {/* CTA Button */}
            {data.ctaButton && (
              <Link href={data.ctaButton.href}>
                <button
                  aria-label={`CTA: ${data.ctaButton.text}`}
                  className="transition-all duration-300 transform hover:scale-105 w-fit mt-4 sm:mt-6 bg-earthy-cocoa hover:bg-earthy-cocoa text-white font-baru-regularbold py-2 sm:py-3 px-6 sm:px-8 rounded-lg text-sm sm:text-base"
                >
                  {data.ctaButton.text}
                </button>
              </Link>
            )}
          </div>

          {/* === IMAGE SECTION - Positioned for Right Alignment === */}
          <div
            className="relative
                       w-11/12 ml-auto   // For small screens: takes 11/12th width and pushes to the right
                       lg:w-[50%] lg:ml-auto // For large screens: takes 50% width and pushes to the right
                       h-72 sm:h-80 lg:h-[32.5rem]
                       rounded-l-full overflow-hidden" // Left side rounded, right side straight
          >
            <Image
              className="object-cover"
              src={data.image.src}
              alt={data.image.alt}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
            />
          </div>
        </div>
      </div>
    </section>
  )
}