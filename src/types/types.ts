

import {aboutSectionData,partnershipSectionData,careersSectionData } from "@/store";
import React, { JSX } from "react";

// Types for better type safety
interface Video {
  src: string;
  title: string;
  description?: string;
}

interface TrustIndicator {
  value: string;
  label: string;
  icon?: React.ComponentType;
}

interface FloatingElement {
  position: { top: string; left: string };
  icon: React.ComponentType;
  className: string;
  delay: number;
  duration: number;
}

interface Button {
  text: string;
  href: string;
  variant: 'primary' | 'secondary';
  icon?: React.ComponentType;
}

interface HeroContent {
  subtitle: string;
  title: {
    prefix: string;
    highlight: string;
    suffix: string;
  };
  description: string;
  buttons: Button[];
}

interface HeroSectionProps {
  content?: HeroContent;
  videos?: Video[];
  backgroundImage?: string;
  trustIndicators?: TrustIndicator[];
  floatingElements?: FloatingElement[];
  autoPlayInterval?: number;
  transitionDuration?: number;
  particleCount?: number;
  showVideoControls?: boolean;
  showTrustIndicators?: boolean;
  className?: string;
}



type HomePageProps = {
  aboutData?: typeof aboutSectionData;
  partnershipData?: typeof partnershipSectionData;
  careersData?: typeof careersSectionData;
  showHero?: boolean;
  showProductRange?: boolean;
  customSections?: React.ComponentType[];
};

// Type definitions
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

interface ContactInfo {
  type: 'location' | 'email' | 'phone' | 'hours';
  title: string;
  primary: string;
  secondary?: string;
  extra?: string;
  icon: JSX.Element;
  badge?: string;
}

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  message: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  company?: string;
  message?: string;
}

// Section component interfaces
interface Feature {
  title: string;
  description: string;
}

interface ImageData {
  src: string;
  alt: string;
}

interface CtaButton {
  text: string;
  href: string;
}

interface AboutSectionData {
  title: string;
  features: Feature[];
  image: ImageData;
  ctaButton: CtaButton;
}

interface PartnershipSectionData {
  title: string;
  image: ImageData;
  paragraphs: string[];
  ctaButton: CtaButton;
}

interface CareersSectionData {
  title: string;
  description: string;
  ctaButton: CtaButton;
  iconDescription: string;
}

// API Request/Response types
interface ContactSubmissionRequest {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
}

interface ContactSubmissionResponse {
  success: boolean;
  id: string;
}

interface ProductCreateRequest {
  name: string;
  description: string;
  details?: string;
  price?: string;
  stock?: string;
  specifications?: Array<{
    key: string;
    value: any;
  }>;
  categoryIds?: string[];
  tagIds?: string[];
  imageUrls?: string[];
}

interface ProductUpdateRequest extends Partial<ProductCreateRequest> {
  id: string;
}

interface ApiErrorResponse {
  error: string;
}

interface ApiSuccessResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// Product related types
interface ProductImage {
  id: string;
  url: string;
  alt?: string;
  isFeatured: boolean;
}

interface ProductCategory {
  id: string;
  name: string;
  description?: string;
}

interface ProductTag {
  id: string;
  name: string;
}

interface ProductSpecification {
  id: string;
  key: string;
  value: any;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  details?: string;
  price?: string;
  stock: string;
  createdAt: string;
  updatedAt: string;
  images: ProductImage[];
  categories: ProductCategory[];
  tags: ProductTag[];
  specifications: ProductSpecification[];
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
}

interface ContactPageConfig {
  hero: {
    title: JSX.Element;
    description: JSX.Element;
    backgroundSrc: string;
    primaryButton: {
      text: string;
      href: string;
    };
    secondaryButton: {
      text: string;
      href: string;
    };
  };
  contactForm: {
    title: string;
    subtitle: string;
    responseTime: string;
  };
  contactInfo: {
    title: string;
    subtitle: string;
  };
  locationSection: {
    imageSrc: string;
    imageAlt: string;
    title: string;
    description: string;
  };
  faqSection: {
    title: string;
    subtitle: string;
    ctaTitle: string;
    ctaDescription: string;
    ctaButtonText: string;
  };
  colors: {
    primary: string;
    accent: string;
    base: string;
  };
}
export type {
  Video,
  TrustIndicator,
  FloatingElement,
  Button,
  HeroContent,
  HeroSectionProps,
  HomePageProps,
  ContactPageConfig,
  ContactInfo, // ✅ Correct type name
  FormErrors,
  FormData,
  FAQ,
  Feature,
  ImageData,
  CtaButton,
  AboutSectionData,
  PartnershipSectionData,
  CareersSectionData,
  ContactSubmissionRequest,
  ContactSubmissionResponse,
  ProductCreateRequest,
  ProductUpdateRequest,
  ApiErrorResponse,
  ApiSuccessResponse
};