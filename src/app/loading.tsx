/**
 * Global loading component for Next.js
 * 
 * Displays during page transitions and data loading
 */
export default function Loading() {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm z-50">
      <div className="flex flex-col items-center">
        <div className="relative">
          {/* Spinner */}
          <div className="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
          
          {/* Inner circle */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full"></div>
          
          {/* Pulsing dot */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-primary-500 rounded-full animate-pulse"></div>
        </div>
        <p className="mt-4 text-earth-700 font-medium">Loading...</p>
      </div>
    </div>
  );
}
