import React from 'react';
import { Metadata } from 'next';
import MainLayout from '@/components/layouts/MainLayout';
import { generatePageMetadata } from '@/lib/seo';
import Image from 'next/image';

export const metadata: Metadata = generatePageMetadata({
  title: 'Sustainable Farming Practices',
  description: 'Discover AfricSource\'s commitment to sustainable farming practices that protect the environment while ensuring premium quality agricultural products.',
  path: '/sustainable-farming'
});

export default function SustainableFarmingPage() {
  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-green-50">
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/90 to-green-700/90"></div>
          <div className="absolute inset-0">
            <Image
              src="/assets/farm-agriculture-face-farmers-with-basket-vegetables-harvest-fresh-produce-farming-sustainability-greenhouse-happy-people-with-teamwork-crate-organic-natural-healthy-food.jpg"
              alt="Sustainable farming practices"
              fill
              className="object-cover"
              priority
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Sustainable Farming
            </h1>
            <p className="text-xl md:text-2xl text-emerald-100 max-w-3xl mx-auto">
              Nurturing the earth while delivering premium African agricultural products through environmentally responsible practices
            </p>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
              <div>
                <h2 className="text-3xl font-bold text-emerald-800 mb-6">
                  Our Commitment to Sustainability
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  At AfricSource, we believe that sustainable farming is not just an option—it's our responsibility. 
                  We work closely with local farmers across Africa to implement practices that protect our environment 
                  while ensuring the highest quality products for our global customers.
                </p>
                <p className="text-lg text-gray-700">
                  Our sustainable approach encompasses soil health, water conservation, biodiversity protection, 
                  and fair trade practices that benefit both the environment and farming communities.
                </p>
              </div>
              <div className="relative h-96 rounded-xl overflow-hidden shadow-2xl">
                <Image
                  src="/assets/portrait-people-team-agriculture-farm-with-tools-wheelbarrow-equipment-planting-diverse-group-man-woman-with-strategy-planning-collaboration-growth-sustainability.jpg"
                  alt="Sustainable farming team"
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Practices Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-emerald-800 mb-4">Soil Health</h3>
                <p className="text-gray-600">
                  Implementing crop rotation, organic composting, and natural fertilizers to maintain soil fertility and structure.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-emerald-800 mb-4">Water Conservation</h3>
                <p className="text-gray-600">
                  Utilizing drip irrigation, rainwater harvesting, and efficient water management systems to preserve this precious resource.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-emerald-800 mb-4">Biodiversity</h3>
                <p className="text-gray-600">
                  Promoting diverse crop varieties and protecting natural habitats to maintain ecological balance.
                </p>
              </div>
            </div>

            {/* Impact Section */}
            <div className="bg-emerald-800 rounded-2xl p-12 text-white text-center">
              <h2 className="text-3xl font-bold mb-6">Our Environmental Impact</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">50%</div>
                  <p className="text-emerald-100">Reduction in water usage through efficient irrigation</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">100+</div>
                  <p className="text-emerald-100">Farming communities supported with sustainable practices</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">25%</div>
                  <p className="text-emerald-100">Increase in soil organic matter through our methods</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  );
}
