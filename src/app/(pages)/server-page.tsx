import { Metadata } from 'next';
import { generatePageMetadata, generateWebsiteStructuredData, generateLocalBusinessStructuredData } from '@/lib/seo';
import HomePage from './page';

// Enhanced homepage metadata
export const metadata: Metadata = generatePageMetadata({
  title: 'Premium African Agricultural Exports & Sustainable Sourcing',
  description: 'AfricSource connects global buyers with Africa\'s finest agricultural products. Sustainable sourcing, fair trade, and premium quality commodities from trusted African farmers and producers.',
  path: '/',
});

export default function ServerHomePage() {
  const websiteStructuredData = generateWebsiteStructuredData();
  const businessStructuredData = generateLocalBusinessStructuredData();

  return (
    <>
      {/* Website Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteStructuredData),
        }}
      />
      
      {/* Local Business Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(businessStructuredData),
        }}
      />
      
      <HomePage />
    </>
  );
}
