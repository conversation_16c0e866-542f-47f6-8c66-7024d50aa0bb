import { Metadata } from 'next';
import MainLayout from "@/components/layouts/MainLayout";
import { generatePageMetadata } from '@/lib/seo';

// Default metadata for pages
export const metadata: Metadata = generatePageMetadata({
  title: 'AfricSource - Premium African Commodity Exports',
  description: 'Connecting global buyers with Africa\'s finest agricultural and mineral products. Trusted, transparent, and sustainable sourcing from verified suppliers.',
  path: '/',
});

export const viewport = {
  width: 'device-width',
  initialScale: 1.0,
  // You can add more properties if needed, for example:
  // maximumScale: 1.0,
  // userScalable: 'no',
};
/**
 * Layout for all pages in the (pages) group
 * Wraps all pages with the MainLayout component
 */
export default function PagesLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <MainLayout>{children}</MainLayout>;
}
