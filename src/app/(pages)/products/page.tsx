"use client";

import React, { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";


// Product interface for Appwrite data
interface Product {
  $id?: string;
  id?: string;
  name: string;
  description: string;
  details?: string;
  price?: string;
  stock?: string;
  slug?: string;
  images?: Array<{
    url: string;
    alt?: string;
    isFeatured?: boolean;
  }>;
  categories?: Array<{
    name: string;
    description?: string;
  }>;
  tags?: Array<{
    name: string;
  }>;
  specifications?: Record<string, any>;
}



export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Handle client-side mounting to prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch products with proper error handling
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/products", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        cache: "no-store", // Ensure fresh data
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Validate the response data
      if (!Array.isArray(data)) {
        throw new Error("API response is not an array");
      }

      setProducts(data);
      setFilteredProducts(data);
    } catch (error) {
      console.error("Error fetching products:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch products"
      );

      // Set mock data for development/testing
      const mockProducts = [
        {
          $id: "1",
          name: "Sample Product",
          categories: [{ name: "Sample Category" }],
          description: "This is a sample product",
          details: "Sample details",
          images: [{ url: "https://via.placeholder.com/300" }],
          specifications: {
            origin: "Sample Origin",
            quality: "Premium",
            packaging: "Standard",
            certifications: ["ISO 9001"],
            moq: "100 units",
            leadTime: "2-3 weeks",
          },
        },
      ];
      setProducts(mockProducts);
      setFilteredProducts(mockProducts);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (mounted) {
      fetchProducts();
    }
  }, [mounted, fetchProducts]);

  // Filter products when search or category changes
  useEffect(() => {
    let result = [...products];

    if (selectedCategory !== "All") {
      result = result.filter((product) => {
        // Handle both array and single category formats
        if (!product.categories) return false;
        return Array.isArray(product.categories)
          ? product.categories.some(cat => cat.name === selectedCategory)
          : false;
      });
    }

    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      result = result.filter((product) => {
        const categoryNames = product.categories?.map(cat => cat.name).join(' ') || '';

        return (
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          categoryNames.toLowerCase().includes(query)
        );
      });
    }

    setFilteredProducts(result);
  }, [selectedCategory, searchQuery, products]);

  // Extract categories safely
  const categories = [
    "All",
    ...Array.from(
      new Set(
        products
          .flatMap((product) =>
            product.categories?.map(cat => cat.name) || []
          )
          .filter(Boolean) // Remove undefined/null values
      )
    ),
  ];



  // Prevent hydration issues by not rendering until mounted
  if (!mounted || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && products.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <svg
              className="w-10 h-10 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-medium text-gray-800 mb-2">
            Error Loading Products
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-all duration-300"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 mt-15">
      {/* Error banner if there was an error but we have fallback data */}
      {error && products.length > 0 && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                API connection failed. Showing sample data. Error: {error}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Page Header - Clean & Simple */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-8 py-10">
          <div className="text-center">
            <h1 className="text-4xl font-baru-bold text-forest-green mb-4 tracking-wide">
              Export Products
            </h1>
            <p className="text-lg font-baru-regular text-deep-charcoal">
              Premium commodities sourced from certified suppliers...
            </p>
          </div>
        </div>
      </div>

      {/* Search & Navigation Bar */}
      <div className="bg-white shadow-sm sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-8 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <input
                type="search"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent bg-gray-50 transition-all duration-300 focus:shadow-lg"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg
                  className="h-5 w-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            {/* Category Pills */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                    selectedCategory === category
                      ? "bg-forest-green text-white shadow-lg"
                      : "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:shadow-md"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="max-w-6xl mx-auto px-8 py-12">
        {/* Results Info */}
        <div className="mb-8">
          <p className="text-gray-600">
            {filteredProducts.length}{" "}
            {filteredProducts.length === 1 ? "product" : "products"}
            {selectedCategory !== "All" && ` in ${selectedCategory}`}
            {searchQuery && ` matching "${searchQuery}"`}
          </p>
        </div>

        {filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 lg:gap-12">
           {filteredProducts.map((product, index) => (
  <div
    key={product.$id || product.id}
    className="transform hover:scale-105 transition-all duration-300"
  >
    <Link
      href={`products/${product.$id || product.id}`}
      className="text-center group block"
    >
      {/* Product Image */}
      <div className="relative w-full aspect-square rounded-full overflow-hidden shadow-lg mb-6 mx-auto transition-all duration-500 group-hover:shadow-2xl">
        <Image
          src={product.images?.[0]?.url || "https://via.placeholder.com/300"}
          alt={product.name}
          fill
          sizes="(max-width: 768px) 100vw,
                 (max-width: 1200px) 50vw,
                 25vw"
          className="object-cover transition-transform duration-700 group-hover:scale-110"
          onError={() => {
            // Note: Next.js Image does NOT support onError natively,
            // So fallback should be handled elsewhere or with next.config.js domains.
          }}
          priority={index < 4} // optionally prioritize first few images
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
        <div className="absolute inset-0 bg-emerald-600/10 opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
      </div>

      {/* Product Name */}
      <h3 className="text-xl lg:text-xl font-light text-gray-800 transition-colors duration-300 group-hover:text-emerald-600">
        {product.name}
      </h3>
    </Link>
  </div>
))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center transition-all duration-500 hover:bg-gray-200">
              <svg
                className="w-10 h-10 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              No products found
            </h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search or category filter
            </p>
            <button
              onClick={() => {
                setSelectedCategory("All");
                setSearchQuery("");
              }}
              className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              Reset Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
