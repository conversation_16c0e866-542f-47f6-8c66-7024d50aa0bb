import { Metadata } from 'next';
import { generateProductMetadata, generateProductStructuredData, generateBreadcrumbStructuredData } from '@/lib/seo';

// Fetch product data for metadata generation
async function getProduct(productId: string) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/products`, {
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (!response.ok) {
      return null;
    }

    const products = await response.json();
    return products.find((p: any) => p.$id === productId) || null;
  } catch (error) {
    console.error('Error fetching product for metadata:', error);
    return null;
  }
}

// Generate structured data for product page
export function generateProductPageStructuredData(product: any) {
  const productStructuredData = generateProductStructuredData(product);

  const breadcrumbStructuredData = generateBreadcrumbStructuredData([
    { name: 'Home', url: '/' },
    { name: 'Products', url: '/products' },
    { name: product.name, url: `/products/${product.$id}` },
  ]);

  return {
    product: productStructuredData,
    breadcrumb: breadcrumbStructuredData,
  };
}

export async function generateMetadata({ params }: { params: Promise<{ productId: string }> }): Promise<Metadata> {
  const { productId } = await params;
  const product = await getProduct(productId);
  
  if (!product) {
    return {
      title: 'Product Not Found | AfricSource',
      description: 'The requested product could not be found.',
      robots: {
        index: false,
        follow: false,
      },
    };
  }
  
  return generateProductMetadata({
    name: product.name,
    description: product.description,
    images: product.images,
    price: product.price,
    categories: product.categories,
    tags: product.tags
  });
}
