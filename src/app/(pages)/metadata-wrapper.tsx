import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo';

// Homepage metadata
export const homeMetadata: Metadata = generatePageMetadata({
  title: 'Premium African Agricultural Exports & Sustainable Sourcing',
  description: 'AfricSource connects global buyers with Africa\'s finest agricultural products. Sustainable sourcing, fair trade, and premium quality commodities from trusted African farmers.',
  path: '/',
});

// Products page metadata
export const productsMetadata: Metadata = generatePageMetadata({
  title: 'Premium African Agricultural Products & Commodities',
  description: 'Explore our extensive range of premium African agricultural exports including cocoa, cashews, coconuts, spices, and more. Direct from source with quality assurance.',
  path: '/products',
});

// About page metadata
export const aboutMetadata: Metadata = generatePageMetadata({
  title: 'About AfricSource - Connecting Africa to Global Markets',
  description: 'Learn about AfricSource\'s mission to empower African farmers through sustainable agriculture, fair trade practices, and direct market access to global buyers.',
  path: '/about',
});

// Contact page metadata
export const contactMetadata: Metadata = generatePageMetadata({
  title: 'Contact AfricSource - Get in Touch for Premium African Exports',
  description: 'Contact AfricSource for premium African agricultural exports, partnership opportunities, and sustainable sourcing solutions. We connect global buyers with African farmers.',
  path: '/contact',
});

// Sustainability page metadata
export const sustainabilityMetadata: Metadata = generatePageMetadata({
  title: 'Sustainable Agriculture & Environmental Responsibility',
  description: 'Discover AfricSource\'s commitment to sustainable farming practices, environmental conservation, and community empowerment across Africa.',
  path: '/sustainability',
});
