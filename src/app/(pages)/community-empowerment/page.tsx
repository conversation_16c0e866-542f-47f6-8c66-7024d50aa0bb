import React from 'react';
import { Metadata } from 'next';
import MainLayout from '@/components/layouts/MainLayout';
import { generatePageMetadata } from '@/lib/seo';
import Image from 'next/image';

export const metadata: Metadata = generatePageMetadata({
  title: 'Community Empowerment & Fair Trade',
  description: 'Discover how AfricSource empowers local farming communities through fair trade practices, education, and sustainable economic development across Africa.',
  path: '/community-empowerment'
});

export default function CommunityEmpowermentPage() {
  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50">
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/90 to-teal-700/90"></div>
          <div className="absolute inset-0">
            <Image
              src="/assets/portrait-african-american-worker-holding-fresh-green-lettuce-grown-hydroponic-controlled-enviroment-delivery-smiling-woman-showing-freshly-harvested-salad-grown-modern-greenhouse.jpg"
              alt="Community empowerment"
              fill
              className="object-cover"
              priority
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Community Empowerment
            </h1>
            <p className="text-xl md:text-2xl text-emerald-100 max-w-3xl mx-auto">
              Building stronger communities through fair trade, education, and sustainable economic opportunities
            </p>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
              <div>
                <h2 className="text-3xl font-bold text-emerald-800 mb-6">
                  Empowering African Communities
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  At AfricSource, we believe that true success comes from lifting up the communities we work with. 
                  Our community empowerment initiatives focus on creating sustainable economic opportunities, 
                  providing education and training, and ensuring fair compensation for local farmers and workers.
                </p>
                <p className="text-lg text-gray-700">
                  Through partnerships with local organizations and direct investment in community development, 
                  we're building a more equitable and prosperous future for African agricultural communities.
                </p>
              </div>
              <div className="relative h-96 rounded-xl overflow-hidden shadow-2xl">
                <Image
                  src="/assets/black-people-portrait-farming-with-confidence-harvest-agriculture-agro-business-together-nature-young-african-group-farmers-with-bucket-crops-organic-food-fresh-produce.jpg"
                  alt="African farmers"
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Programs Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-emerald-800 mb-4">Fair Trade Practices</h3>
                <p className="text-gray-600">
                  Ensuring farmers receive fair compensation for their products with transparent pricing and direct trade relationships.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-emerald-800 mb-4">Education & Training</h3>
                <p className="text-gray-600">
                  Providing agricultural training, business skills development, and educational resources to enhance productivity.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-emerald-800 mb-4">Community Development</h3>
                <p className="text-gray-600">
                  Investing in infrastructure, healthcare, and social programs that benefit entire communities.
                </p>
              </div>
            </div>

            {/* Impact Stories */}
            <div className="bg-white rounded-2xl shadow-xl p-12 mb-16">
              <h2 className="text-3xl font-bold text-emerald-800 text-center mb-12">
                Community Impact Stories
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden">
                    <Image
                      src="/assets/indian_farmer.jpg"
                      alt="Farmer success story"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h4 className="text-xl font-semibold text-emerald-800 mb-4">Amara's Success Story</h4>
                  <p className="text-gray-600">
                    "Through AfricSource's training program, I learned modern farming techniques that increased 
                    my yield by 40%. The fair trade partnership has allowed me to send my children to school 
                    and improve our family's quality of life."
                  </p>
                </div>
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden">
                    <Image
                      src="/assets/asian_rice_farmers.jpg"
                      alt="Community development"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h4 className="text-xl font-semibold text-emerald-800 mb-4">Village Transformation</h4>
                  <p className="text-gray-600">
                    "Our entire village has benefited from the community development programs. We now have 
                    access to clean water, improved roads, and a new school. The economic opportunities 
                    have kept our young people from migrating to cities."
                  </p>
                </div>
              </div>
            </div>

            {/* Impact Statistics */}
            <div className="bg-emerald-800 rounded-2xl p-12 text-white text-center">
              <h2 className="text-3xl font-bold mb-6">Our Community Impact</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">5,000+</div>
                  <p className="text-emerald-100">Farmers directly supported</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">150</div>
                  <p className="text-emerald-100">Communities empowered</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">$2M+</div>
                  <p className="text-emerald-100">Invested in community development</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-emerald-300 mb-2">95%</div>
                  <p className="text-emerald-100">Farmer satisfaction rate</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  );
}
