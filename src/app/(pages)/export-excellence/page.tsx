import React from 'react';
import { Metadata } from 'next';
import MainLayout from '@/components/layouts/MainLayout';
import { generatePageMetadata } from '@/lib/seo';
import Image from 'next/image';

export const metadata: Metadata = generatePageMetadata({
  title: 'Export Excellence & Global Standards',
  description: 'Learn about AfricSource\'s commitment to export excellence, international quality standards, and seamless global logistics for African agricultural products.',
  path: '/export-excellence'
});

export default function ExportExcellencePage() {
  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50">
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-orange-600/90 to-amber-700/90"></div>
          <div className="absolute inset-0">
            <Image
              src="/assets/transportation-logistics-container-cargo-ship-cargo-plane-3d-rendering-illustration.jpg"
              alt="Global export logistics"
              fill
              className="object-cover"
              priority
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Export Excellence
            </h1>
            <p className="text-xl md:text-2xl text-orange-100 max-w-3xl mx-auto">
              Setting the global standard for African agricultural exports through quality, reliability, and seamless logistics
            </p>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
              <div>
                <h2 className="text-3xl font-bold text-orange-800 mb-6">
                  World-Class Export Standards
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  AfricSource has established itself as a leader in African agricultural exports by maintaining 
                  the highest international standards. Our comprehensive quality control processes ensure that 
                  every product meets or exceeds global requirements.
                </p>
                <p className="text-lg text-gray-700">
                  From farm to destination, we manage every aspect of the export process with precision, 
                  transparency, and unwavering commitment to excellence.
                </p>
              </div>
              <div className="relative h-96 rounded-xl overflow-hidden shadow-2xl">
                <Image
                  src="/assets/open-doors-greengrocers-car-delivery-food-vegetables-markets-city-shops.jpg"
                  alt="Export delivery"
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Standards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-orange-800 mb-4">Quality Assurance</h3>
                <p className="text-gray-600">
                  Rigorous testing and certification processes ensuring compliance with international food safety standards.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-orange-800 mb-4">Timely Delivery</h3>
                <p className="text-gray-600">
                  Advanced logistics management ensuring on-time delivery to destinations worldwide with full traceability.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-orange-800 mb-4">Documentation</h3>
                <p className="text-gray-600">
                  Complete documentation and certification packages for seamless customs clearance and compliance.
                </p>
              </div>
            </div>

            {/* Certifications Section */}
            <div className="bg-white rounded-2xl shadow-xl p-12 mb-16">
              <h2 className="text-3xl font-bold text-orange-800 text-center mb-12">
                International Certifications
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-orange-600 font-bold text-lg">ISO</span>
                  </div>
                  <h4 className="font-semibold text-gray-800">ISO 22000</h4>
                  <p className="text-sm text-gray-600">Food Safety Management</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-green-600 font-bold text-lg">HACCP</span>
                  </div>
                  <h4 className="font-semibold text-gray-800">HACCP</h4>
                  <p className="text-sm text-gray-600">Hazard Analysis</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 font-bold text-lg">FDA</span>
                  </div>
                  <h4 className="font-semibold text-gray-800">FDA</h4>
                  <p className="text-sm text-gray-600">US Food & Drug Admin</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-purple-600 font-bold text-lg">EU</span>
                  </div>
                  <h4 className="font-semibold text-gray-800">EU Standards</h4>
                  <p className="text-sm text-gray-600">European Union Compliance</p>
                </div>
              </div>
            </div>

            {/* Statistics Section */}
            <div className="bg-orange-800 rounded-2xl p-12 text-white text-center">
              <h2 className="text-3xl font-bold mb-6">Export Performance</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                  <div className="text-4xl font-bold text-orange-300 mb-2">50+</div>
                  <p className="text-orange-100">Countries served worldwide</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-orange-300 mb-2">99.8%</div>
                  <p className="text-orange-100">On-time delivery rate</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-orange-300 mb-2">100%</div>
                  <p className="text-orange-100">Quality compliance rate</p>
                </div>
                <div>
                  <div className="text-4xl font-bold text-orange-300 mb-2">24/7</div>
                  <p className="text-orange-100">Customer support availability</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  );
}
