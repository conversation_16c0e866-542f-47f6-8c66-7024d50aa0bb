import { Metadata } from "next";

export const metadata: Metadata = {
  title: "AfricSource - Premium Agricultural Exports & Sustainable Partnerships",
  description:
    "AfricSource offers premium quality agricultural exports, direct partnerships with local farmers, sustainable farming practices, and global logistics solutions. Learn about our mission, careers, and partnership opportunities.",
  keywords: [
    "Agricultural exports",
    "Sustainable farming",
    "Local farmers partnership",
    "Global logistics",
    "Quality assurance",
    "Careers at AfricSource",
    "Partnership opportunities",
    "Premium produce",
  ],
  authors: [{ name: "AfricSource Team", url: "https://africsource.com" }],
  creator: "AfricSource",
  publisher: "AfricSource",
  openGraph: {
    title: "AfricSource - Premium Agricultural Exports & Sustainable Partnerships",
    description:
      "Supporting farmers and delivering premium agricultural produce globally. Explore our mission, partnerships, and career opportunities.",
    url: "https://africsource.com",
    siteName: "AfricSource",
    images: [
      {
        url: "/assets/Sun Gold.png/",
        width: 1200,
        height: 630,
        alt: "AfricSource - Premium Agricultural Exports",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AfricSource - Premium Agricultural Exports & Sustainable Partnerships",
    description:
      "Supporting farmers and delivering premium agricultural produce globally. Explore our mission, partnerships, and career opportunities.",
    site: "@AfricSource",
    creator: "@AfricSource",
    images: ["/assets/Sun Gold.png/"],
  },
  robots: {
    index: true,
    follow: true,
  },
};
