@import "tailwindcss";

@font-face {
  font-family: 'BaruBold';
  src: url('/Baru Sans/BaruSansDemo-Bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'BaruSemiBold';
  src: url('/Baru Sans/BaruSansDemo-SemiBold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'BaruRegular';
  src: url('/Baru Sans/BaruSansDemo-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

:root {
  --background: #ffffff;
  --foreground: #28241f;
   --deep-charcoal: #212121;
  --forest-green: #4CAF50;
  --earthy-cocoa: #5D4037;
  --sun-gold: #FFCA00;
  --white: #FFFFFF;
  --harvest-green: #5BD05F;
}


/* Background Colors */
.bg-background { background-color: var(--background); }
.bg-foreground { background-color: var(--foreground); }
.bg-deep-charcoal { background-color: var(--deep-charcoal); }
.bg-forest-green { background-color: var(--forest-green); }
.bg-earthy-cocoa { background-color: var(--earthy-cocoa); }
.bg-sun-gold { background-color: var(--sun-gold); }
.bg-white { background-color: var(--white); }
.bg-harvest-green { background-color: var(--harvest-green); }

/* Hover Background Colors */
.hover\:bg-background:hover { background-color: var(--background); }
.hover\:bg-foreground:hover { background-color: var(--foreground); }
.hover\:bg-deep-charcoal:hover { background-color: var(--deep-charcoal); }
.hover\:bg-forest-green:hover { background-color: var(--forest-green); }
.hover\:bg-earthy-cocoa:hover { background-color: var(--earthy-cocoa); }
.hover\:bg-sun-gold:hover { background-color: var(--sun-gold); }
.hover\:bg-white:hover { background-color: var(--white); }
.hover\:bg-harvest-green:hover { background-color: var(--harvest-green); }

/* Text Colors */
.text-background { color: var(--background); }
.text-foreground { color: var(--foreground); }
.text-deep-charcoal { color: var(--deep-charcoal); }
.text-forest-green { color: var(--forest-green); }
.text-earthy-cocoa { color: var(--earthy-cocoa); }
.text-sun-gold { color: var(--sun-gold); }
.text-white { color: var(--white); }
.text-harvest-green { color: var(--harvest-green); }

/* Hover Text Colors */
.hover\:text-background:hover { color: var(--background); }
.hover\:text-foreground:hover { color: var(--foreground); }
.hover\:text-deep-charcoal:hover { color: var(--deep-charcoal); }
.hover\:text-forest-green:hover { color: var(--forest-green); }
.hover\:text-earthy-cocoa:hover { color: var(--earthy-cocoa); }
.hover\:text-sun-gold:hover { color: var(--sun-gold); }
.hover\:text-white:hover { color: var(--white); }
.hover\:text-harvest-green:hover { color: var(--harvest-green); }


.font-baru-bold {
  font-family: 'BaruBold', sans-serif;
}

.font-baru-regularbold {
  font-family: 'BaruRegular', sans-serif;
}
.font-baru-semibold {
  font-family: 'BaruSemiBold', sans-serif;
}
/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  background-color: white;
 /* font-family: 'BaruBold', sans-serif; */
  -webkit-font-smoothing: antialiased;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  font-weight: bold;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  h1 {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3.75rem;
  }
}

h2 {
  font-size: 1.875rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  h2 {
    font-size: 2.25rem;
  }
}

@media (min-width: 1024px) {
  h2 {
    font-size: 3rem;
  }
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
}

@media (min-width: 768px) {
  h3 {
    font-size: 1.875rem;
  }
}

p {
  font-size: 14px;
  line-height: 1.6;
  /* color: #4a4137; */
  text-align: justify;
  hyphens: auto;
  text-align-last: left;
}

@media (min-width: 768px) {
  p {
    font-size: 1.125rem;
  }
}

/* Container utilities */
.container-custom {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.container-narrow {
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-narrow {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Section utilities */
.section {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .section {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .section {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  pointer-events: none;
}

.btn-primary {
  background-color: #d98c2e;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:hover {
  background-color: #c47125;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-secondary {
  background-color: #5ea75e;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary:hover {
  background-color: #4a8c4a;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid #d98c2e;
  color: #d98c2e;
}

.btn-outline:hover {
  background-color: #fdf6ed;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s;
}

.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-0.25rem);
}

/* Animation utilities */
.animate-on-scroll {
  opacity: 0;
  transition: all 0.7s;
}

.animate-fade-in-up {
  animation: fadeInUp 0.7s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hover-bloom {
  transition: transform 0.3s;
}

.hover-bloom:hover {
  transform: scale(1.05);
}

/* Background effects */
.bg-texture {
  background-image: url('/textures/grain.svg');
  background-repeat: repeat;
  opacity: 0.03;
  position: absolute;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

/* For the wavy bottom effect */


/* Hero section integration styles */
.bg-forest-green {
  background-color: var(--forest-green);
}

/* Seamless transition between hero and highlights */
.hero-highlights-transition {
  background: linear-gradient(180deg, var(--forest-green) 0%, rgba(0,0,0,0.8) 100%);
}

/* Additional positioning fix for wavy bottom - ensures no gaps */
.section-wave-bottom-highlights {
  position: relative;
  overflow: visible; /* Allow the wave to extend beyond the section if needed */
}

.section-wave-bottom-highlights::after {
  /* Ensure the wave is perfectly aligned with the section bottom */
  display: block;
  margin: 0;
  padding: 0;
}