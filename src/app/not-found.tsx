import Link from "next/link";
import Image from "next/image";

/**
 * Custom 404 Not Found page
 * 
 * Displays when a user navigates to a non-existent route
 */
export default function NotFound() {
  return (
   <div className="min-h-screen flex items-center justify-center bg-earth-50 px-4">
  <div className="max-w-md w-full text-center">
    <div className="relative w-32 h-32 mx-auto mb-8">
      <Image
        src="/globe.svg"
        alt="AfricSource Logo"
        fill
        className="object-contain opacity-30"
      />
    </div>
    
    <h1 className="text-6xl font-bold text-earth-800 mb-4">404</h1>
    <h2 className="text-2xl font-semibold text-earth-700 mb-6">Page Not Found</h2>
    
    <p className="text-earth-600 mb-8">
      {`The page you're looking for doesn't exist or has been moved.`}
    </p>
    
    <Link 
      href="/"
      className="btn-primary inline-flex items-center"
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className="h-5 w-5 mr-2" 
        viewBox="0 0 20 20" 
        fill="currentColor"
      >
        <path 
          fillRule="evenodd" 
          d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" 
          clipRule="evenodd" 
        />
      </svg>
      Return to Homepage
    </Link>
  </div>
</div>

  );
}
