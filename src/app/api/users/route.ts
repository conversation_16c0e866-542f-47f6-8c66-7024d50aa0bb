import { NextRequest, NextResponse } from "next/server";
import { AppwriteUserService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { hash } from 'bcrypt';

// GET all users (admin only)
export async function GET(request: NextRequest) {
  try {
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    const users = await AppwriteUserService.getAllUsers({
      orderBy: '$createdAt',
      orderDirection: 'desc'
    });

    // Remove password from response for security
    const safeUsers = users.map(user => ({
      $id: user.$id,
      name: user.name,
      email: user.email,
      role: user.role,
      $createdAt: user.$createdAt,
      $updatedAt: user.$updatedAt
    }));

    return NextResponse.json(safeUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

// POST create a new user (admin only)
export async function POST(request: NextRequest) {
  try {
    // Authenticate and authorize admin user
    const currentUser = await AppwriteServerAuth.protectAdminRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      return NextResponse.json(
        { error: "Name, email, and password are required" },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUsers = await AppwriteUserService.getAllUsers({
      search: body.email,
      limit: 10
    });

    const existingUser = existingUsers.find(user =>
      user.email.toLowerCase() === body.email.toLowerCase()
    );

    if (existingUser) {
      return NextResponse.json(
        { error: "Email already in use" },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hash(body.password, 12);

    // Create user
    const user = await AppwriteUserService.createUser({
      name: body.name,
      email: body.email,
      password: hashedPassword,
      role: body.role || "USER"
    });

    // Return user without password
    const safeUser = {
      $id: user.$id,
      name: user.name,
      email: user.email,
      role: user.role,
      $createdAt: user.$createdAt,
      $updatedAt: user.$updatedAt
    };

    return NextResponse.json(safeUser, { status: 201 });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
