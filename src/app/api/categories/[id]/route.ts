import { NextRequest, NextResponse } from "next/server";
import { AppwriteCategoryService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

// GET a specific category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const category = await AppwriteCategoryService.getCategoryById(id);

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category" },
      { status: 500 }
    );
  }
}

// PUT update a category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await AppwriteCategoryService.getCategoryById(id);

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if name is already taken by another category
    if (body.name !== existingCategory.name) {
      const allCategories = await AppwriteCategoryService.getAllCategories({
        search: body.name,
        limit: 10
      });

      const nameExists = allCategories.find(cat =>
        cat.name.toLowerCase() === body.name.toLowerCase() && cat.$id !== id
      );

      if (nameExists) {
        return NextResponse.json(
          { error: "Category name already exists" },
          { status: 409 }
        );
      }
    }

    // Update category
    const updatedCategory = await AppwriteCategoryService.updateCategory(id, {
      name: body.name,
      description: body.description
    });

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { error: "Failed to update category" },
      { status: 500 }
    );
  }
}

// DELETE a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    // Check if category exists
    const existingCategory = await AppwriteCategoryService.getCategoryById(id);

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Delete category (this will also handle related product-category relationships)
    const success = await AppwriteCategoryService.deleteCategory(id);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to delete category" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { error: "Failed to delete category" },
      { status: 500 }
    );
  }
}
