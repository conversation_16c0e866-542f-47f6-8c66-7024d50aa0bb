import { NextRequest, NextResponse } from "next/server";
import { AppwriteCategoryService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

// GET all categories
export async function GET() {
  try {
    const categories = await AppwriteCategoryService.getAllCategories({
      orderBy: 'name',
      orderDirection: 'asc'
    });

    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { error: "Failed to fetch categories" },
      { status: 500 }
    );
  }
}

// POST create a new category
export async function POST(request: NextRequest) {
  try {
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category already exists
    const existingCategories = await AppwriteCategoryService.getAllCategories({
      search: body.name,
      limit: 10
    });

    const existingCategory = existingCategories.find(cat =>
      cat.name.toLowerCase() === body.name.toLowerCase()
    );

    if (existingCategory) {
      return NextResponse.json(
        { error: "Category already exists" },
        { status: 409 }
      );
    }

    // Create category
    const category = await AppwriteCategoryService.createCategory({
      name: body.name,
      description: body.description
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { error: "Failed to create category" },
      { status: 500 }
    );
  }
}
