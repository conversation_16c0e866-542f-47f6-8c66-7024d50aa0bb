import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { createServerClient, STORAGE_BUCKET_ID, DATABASE_ID, COLLECTIONS } from "@/lib/appwrite";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify authentication
    const authResult = await AppwriteServerAuth.protectAdminRoute();
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { id: fileId } = await params;

    if (!fileId) {
      return NextResponse.json(
        { error: "File ID is required" },
        { status: 400 }
      );
    }

    const { databases, storage } = createServerClient();

    // First, find and delete the image record from the database
    try {
      // Find the image record in the product_images collection
      const imageRecords = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_IMAGES,
        []
      );

      // Find the image record that matches this file ID (extracted from URL)
      const imageRecord = imageRecords.documents.find((doc: any) => 
        doc.url.includes(fileId)
      );

      if (imageRecord) {
        // Delete the image record from the database
        await databases.deleteDocument(
          DATABASE_ID,
          COLLECTIONS.PRODUCT_IMAGES,
          imageRecord.$id
        );
        console.log(`Deleted image record: ${imageRecord.$id}`);
      }
    } catch (dbError) {
      console.error('Error deleting image from database:', dbError);
      // Continue with storage deletion even if database deletion fails
    }

    // Delete the file from Appwrite storage
    try {
      await storage.deleteFile(STORAGE_BUCKET_ID, fileId);
    } catch (storageError) {
      // If storage deletion fails, we should still return success if DB deletion worked
    }

    return NextResponse.json(
      { message: "Image deleted successfully" },
      { status: 200 }
    );

  } catch (error: any) {
    return NextResponse.json(
      {
        error: "Failed to delete image",
        details: error.message
      },
      { status: 500 }
    );
  }
}
