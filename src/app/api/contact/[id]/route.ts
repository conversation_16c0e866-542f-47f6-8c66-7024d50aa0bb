import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { AppwriteContactService } from "@/lib/appwrite-server";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin authentication
    const user = await AppwriteServerAuth.protectAdminRoute(request);
    
    const { id: contactId } = await params;
    const { status } = await request.json();

    if (!contactId) {
      return NextResponse.json(
        { error: "Contact ID is required" },
        { status: 400 }
      );
    }

    if (!status || !['UNREAD', 'READ', 'REPLIED', 'CLOSED'].includes(status)) {
      return NextResponse.json(
        { error: "Valid status is required (UNREAD, READ, REPLIED, CLOSED)" },
        { status: 400 }
      );
    }

    // Update contact status
    const updatedContact = await AppwriteContactService.updateContactStatus(contactId, status);

    return NextResponse.json(updatedContact, { status: 200 });

  } catch (error: any) {
    console.error("Error updating contact status:", error);
    
    if (error.message === 'Unauthorized' || error.message === 'Admin access required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to update contact status" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin authentication
    const user = await AppwriteServerAuth.protectAdminRoute(request);
    
    const { id: contactId } = await params;

    if (!contactId) {
      return NextResponse.json(
        { error: "Contact ID is required" },
        { status: 400 }
      );
    }

    // Get contact by ID
    const contact = await AppwriteContactService.getContactSubmissionById(contactId);

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(contact, { status: 200 });

  } catch (error: any) {
    console.error("Error fetching contact:", error);
    
    if (error.message === 'Unauthorized' || error.message === 'Admin access required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to fetch contact" },
      { status: 500 }
    );
  }
}
