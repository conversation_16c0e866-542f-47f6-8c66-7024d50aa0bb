import { NextRequest, NextResponse } from "next/server";
import { AppwriteTagService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

// GET all tags
export async function GET() {
  try {
    const tags = await AppwriteTagService.getAllTags({
      orderBy: 'name',
      orderDirection: 'asc'
    });

    return NextResponse.json(tags);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return NextResponse.json(
      { error: "Failed to fetch tags" },
      { status: 500 }
    );
  }
}

// POST create a new tag
export async function POST(request: NextRequest) {
  try {
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Tag name is required" },
        { status: 400 }
      );
    }

    // Check if tag already exists
    const existingTags = await AppwriteTagService.getAllTags({
      search: body.name,
      limit: 10
    });

    const existingTag = existingTags.find(tag =>
      tag.name.toLowerCase() === body.name.toLowerCase()
    );

    if (existingTag) {
      return NextResponse.json(
        { error: "Tag already exists" },
        { status: 409 }
      );
    }

    // Create tag
    const tag = await AppwriteTagService.createTag({
      name: body.name
    });

    return NextResponse.json(tag, { status: 201 });
  } catch (error) {
    console.error("Error creating tag:", error);
    return NextResponse.json(
      { error: "Failed to create tag" },
      { status: 500 }
    );
  }
}
