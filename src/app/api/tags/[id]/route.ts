import { NextRequest, NextResponse } from "next/server";
import { AppwriteTagService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

// GET a specific tag
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const tag = await AppwriteTagService.getTagById(id);

    if (!tag) {
      return NextResponse.json(
        { error: "Tag not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(tag);
  } catch (error) {
    console.error("Error fetching tag:", error);
    return NextResponse.json(
      { error: "Failed to fetch tag" },
      { status: 500 }
    );
  }
}

// PUT update a tag
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Tag name is required" },
        { status: 400 }
      );
    }

    // Check if tag exists
    const existingTag = await AppwriteTagService.getTagById(id);

    if (!existingTag) {
      return NextResponse.json(
        { error: "Tag not found" },
        { status: 404 }
      );
    }

    // Check if name is already taken by another tag
    if (body.name !== existingTag.name) {
      const allTags = await AppwriteTagService.getAllTags({
        search: body.name,
        limit: 10
      });

      const nameExists = allTags.find(tag =>
        tag.name.toLowerCase() === body.name.toLowerCase() && tag.$id !== id
      );

      if (nameExists) {
        return NextResponse.json(
          { error: "Tag name already exists" },
          { status: 409 }
        );
      }
    }

    // Update tag
    const updatedTag = await AppwriteTagService.updateTag(id, {
      name: body.name
    });

    return NextResponse.json(updatedTag);
  } catch (error) {
    console.error("Error updating tag:", error);
    return NextResponse.json(
      { error: "Failed to update tag" },
      { status: 500 }
    );
  }
}

// DELETE a tag
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Authenticate and authorize admin user
    const user = await AppwriteServerAuth.protectAdminRoute(request);

    // Check if tag exists
    const existingTag = await AppwriteTagService.getTagById(id);

    if (!existingTag) {
      return NextResponse.json(
        { error: "Tag not found" },
        { status: 404 }
      );
    }

    // Delete tag (this will also handle related product-tag relationships)
    const success = await AppwriteTagService.deleteTag(id);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to delete tag" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting tag:", error);
    return NextResponse.json(
      { error: "Failed to delete tag" },
      { status: 500 }
    );
  }
}
