import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication and get user
    const user = await AppwriteServerAuth.protectRoute(request);

    // Return user data
    return NextResponse.json({
      user: user
    }, { status: 200 });

  } catch (error: any) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch user data" },
      { status: 401 }
    );
  }
}
