import { NextRequest, NextResponse } from "next/server";
import { AppwriteUserService } from "@/lib/appwrite-server";
import { UserRole } from "@/lib/appwrite";

export async function POST(request: NextRequest) {
  try {
    const { name, email, password, role = 'USER' } = await request.json();

    if (!name || !email || !password) {
      return NextResponse.json(
        { error: "Name, email, and password are required" },
        { status: 400 }
      );
    }

    if (password.length < 8) {
      return NextResponse.json(
        { error: "Password must be at least 8 characters long" },
        { status: 400 }
      );
    }

    // Create user in Appwrite
    const user = await AppwriteUserService.createUser({
      name,
      email,
      password,
      role: role === 'ADMIN' ? UserRole.ADMIN : UserRole.USER
    });

    return NextResponse.json({
      user: {
        $id: user.$id,
        name: user.name,
        email: user.email,
        role: user.role
      },
      message: "User created successfully"
    }, { status: 201 });

  } catch (error: any) {
    // Handle specific Appwrite errors
    if (error.code === 409) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: error.message || "Failed to create user" },
      { status: 500 }
    );
  }
}
