import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { AppwriteUserService } from "@/lib/appwrite-server";

export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const user = await AppwriteServerAuth.protectRoute(request);

    const { name, email } = await request.json();

    if (!name || !email) {
      return NextResponse.json(
        { error: "Name and email are required" },
        { status: 400 }
      );
    }

    // Update user profile
    const updatedUser = await AppwriteUserService.updateUser(user.$id, {
      name,
      email
    });

    return NextResponse.json({
      user: updatedUser,
      message: "Profile updated successfully"
    }, { status: 200 });

  } catch (error: any) {
    console.error("Error updating profile:", error);
    return NextResponse.json(
      {
        error: error.message || "Failed to update profile"
      },
      { status: 500 }
    );
  }
}
