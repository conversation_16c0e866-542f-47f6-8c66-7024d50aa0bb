import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { createServerClient } from "@/lib/appwrite";

export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const _user = await AppwriteServerAuth.protectRoute(request);

    const { currentPassword, newPassword } = await request.json();

    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: "Current password and new password are required" },
        { status: 400 }
      );
    }

    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: "New password must be at least 8 characters long" },
        { status: 400 }
      );
    }

    const { account } = createServerClient();

    try {
      // Update password in Appwrite
      await account.updatePassword(newPassword, currentPassword);

      return NextResponse.json({
        message: "Password changed successfully"
      }, { status: 200 });

    } catch (appwriteError: any) {
      // Handle specific Appwrite errors
      if (appwriteError.code === 401) {
        return NextResponse.json(
          { error: "Current password is incorrect" },
          { status: 400 }
        );
      }

      throw appwriteError;
    }

  } catch (error: any) {
    return NextResponse.json(
      {
        error: error.message || "Failed to change password"
      },
      { status: 500 }
    );
  }
}
