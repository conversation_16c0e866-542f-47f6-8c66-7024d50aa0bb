import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { AppwriteUserService } from "@/lib/appwrite-server";

export async function GET(request: NextRequest) {
  try {
    // Get session from cookie
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get('appwrite-session');

    if (!sessionCookie) {
      return NextResponse.json({ user: null });
    }

    // Parse session data
    const sessionData = JSON.parse(sessionCookie.value);
    
    // Verify user still exists and get fresh data
    const user = await AppwriteUserService.getUserById(sessionData.userId);
    
    if (!user) {
      // User no longer exists, clear cookie
      cookieStore.delete('appwrite-session');
      return NextResponse.json({ user: null });
    }

    return NextResponse.json({
      user: {
        id: user.$id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error("Session verification error:", error);
    return NextResponse.json({ user: null });
  }
}
