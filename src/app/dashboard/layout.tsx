import { Metadata } from 'next';

// Prevent dashboard pages from being indexed
export const metadata: Metadata = {
  robots: {
    index: false,
    follow: false,
    noarchive: true,
    nosnippet: true,
    noimageindex: true,
    nocache: true,
  },
  title: 'Dashboard - AfricSource Admin',
  description: 'AfricSource administrative dashboard - private area',
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex, nocache" />
      {children}
    </>
  );
}
