"use client";

import React, { useState, useEffect } from "react";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { Button } from "@/components/atoms/Button";

interface Tag {
  $id: string;
  name: string;
}

export default function TagsPage() {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formName, setFormName] = useState("");
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch tags
  useEffect(() => {
    const fetchTags = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/tags");
        if (!response.ok) {
          throw new Error("Failed to fetch tags");
        }

        const data = await response.json();
        setTags(data);
      } catch (err) {
        console.error("Error fetching tags:", err);
        setError(err.message || "An error occurred while fetching tags");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, []);

  // Reset form
  const resetForm = () => {
    setFormName("");
    setFormError(null);
    setIsEditing(false);
    setEditingId(null);
  };

  // Handle edit
  const handleEdit = (tag: Tag) => {
    setFormName(tag.name);
    setIsEditing(true);
    setEditingId(tag.$id);
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this tag?")) {
      return;
    }

    try {
      const response = await fetch(`/api/tags/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete tag");
      }

      // Remove from state
      setTags(tags.filter((tag) => tag.$id !== id));
    } catch (err) {
      console.error("Error deleting tag:", err);
      alert(err.message || "An error occurred while deleting the tag");
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate
    if (!formName.trim()) {
      setFormError("Tag name is required");
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      if (isEditing && editingId) {
        // Update existing tag
        const response = await fetch(`/api/tags/${editingId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: formName,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to update tag");
        }

        const updatedTag = await response.json();

        // Update state
        setTags(tags.map((tag) => (tag.$id === editingId ? updatedTag : tag)));
      } else {
        // Create new tag
        const response = await fetch("/api/tags", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: formName,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create tag");
        }

        const newTag = await response.json();

        // Add to state
        setTags([...tags, newTag]);
      }

      // Reset form
      resetForm();
    } catch (err) {
      console.error("Error submitting form:", err);
      setFormError(err.message || "An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
   <DashboardLayout>
  <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
      <h1 className="text-2xl font-bold" style={{ color: "#0C4B35" }}>
        Export Tags Management
      </h1>
      <p className="text-gray-600">
        Manage product classification tags for export commodities
      </p>
    </div>
  </div>

  {/* Form */}
  <div className="bg-gradient-to-r text-white from-green-800 to-green-700 rounded-2xl shadow-lg border p-6 mb-8">
    <h2 className="text-lg font-semibold mb-4">
      {isEditing ? "Edit Export Tag" : "Add New Export Tag"}
    </h2>

    <form onSubmit={handleSubmit}>
      {formError && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded-r-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{formError}</p>
            </div>
          </div>
        </div>
      )}

      <div>
        <label htmlFor="name" className="block text-sm font-medium mb-1">
          Tag Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="name"
          value={formName}
          onChange={(e) => setFormName(e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-lg text-white focus:ring-2 focus:border-transparent transition-all duration-200 bg-black"
          style={
            {
              "--tw-ring-color": "#0C4B35",
              "--tw-ring-opacity": "0.5",
            } as React.CSSProperties
          }
          onFocus={(e) => {
            e.target.style.borderColor = "#0C4B35";
            e.target.style.boxShadow = `0 0 0 3px rgba(12, 75, 53, 0.1)`;
          }}
          onBlur={(e) => {
            e.target.style.borderColor = "#e5e7eb";
            e.target.style.boxShadow = "none";
          }}
          placeholder='e.g., Organic, Fair Trade, Premium Grade'
        />

        <p className="text-xs mt-1">
          {`Use descriptive tags like "Export Grade", "Certified Organic", "Premium Quality"`}
        </p>
      </div>

      <div className="mt-6 flex justify-end space-x-3">
        {isEditing && (
          <Button
            type="button"
            variant="ghost"
            onClick={resetForm}
            disabled={isSubmitting}
            className="px-6 py-2 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            Cancel
          </Button>
        )}

        <Button
          type="submit"
          variant="primary"
          isLoading={isSubmitting}
          className="px-6 py-2 text-white font-semibold rounded-lg transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5"
          style={{ backgroundColor: "#0C4B35" }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "#0a3d2a";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "#0C4B35";
          }}
        >
          {isEditing ? "Update Tag" : "Add Tag"}
        </Button>
      </div>
    </form>
  </div>

  {/* Tags List */}
  {isLoading ? (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8 flex justify-center items-center">
      <div className="flex items-center space-x-3">
        <div
          className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2"
          style={{ borderColor: "#0C4B35" }}
        ></div>
        <span className="text-gray-600">Loading export tags...</span>
      </div>
    </div>
  ) : error ? (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
      <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-r-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      <div
        className="px-6 py-4 border-b border-gray-100"
        style={{ backgroundColor: "#f8f9fa" }}
      >
        <div className="flex items-center justify-between">
          <h3
            className="text-lg font-semibold"
            style={{ color: "#0C4B35" }}
          >
            Export Tags ({tags.length})
          </h3>
          <div className="text-sm text-gray-500">
            Manage classification tags for export products
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead style={{ backgroundColor: "#f8f9fa" }}>
            <tr>
              <th
                scope="col"
                className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider"
              >
                Tag Name
              </th>
              <th
                scope="col"
                className="px-6 py-4 text-right text-xs font-semibold uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tags.length > 0 ? (
              tags.map((tag, index) => (
                <tr
                  key={tag.$id}
                  className="hover:bg-gray-50 transition-colors duration-150"
                  style={{
                    backgroundColor: index % 2 === 0 ? "#ffffff" : "#fafafa",
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div
                        className="flex-shrink-0 w-2 h-2 rounded-full mr-3"
                        style={{ backgroundColor: "#F3C800" }}
                      ></div>
                      <div
                        className="text-sm font-medium"
                        style={{ color: "#0C4B35" }}
                      >
                        {tag.name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => handleEdit(tag)}
                        className="text-sm font-medium px-3 py-1 rounded-md transition-colors duration-200 hover:bg-gray-100"
                        style={{ color: "#0C4B35" }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = "#f0f9f3";
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = "transparent";
                        }}
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(tag.$id)}
                        className="text-red-600 hover:text-red-800 text-sm font-medium px-3 py-1 rounded-md transition-colors duration-200 hover:bg-red-50"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={2} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <div className="text-4xl mb-4">🏷️</div>
                    <h3
                      className="text-lg font-medium mb-2"
                      style={{ color: "#0C4B35" }}
                    >
                      No export tags found
                    </h3>
                    <p className="text-gray-500 text-sm mb-4">
                      Start by adding your first product classification tag above.
                    </p>
                    <p className="text-xs text-gray-400">
                      {`Examples: "Premium Grade", "Organic Certified", "Fair Trade", "Export Quality"`}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with helpful info */}
      {tags.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Total tags: {tags.length}</span>
            <span>
              Use tags to classify and organize your export products
            </span>
          </div>
        </div>
      )}
    </div>
  )}
</DashboardLayout>

  );
}
