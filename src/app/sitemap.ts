import { MetadataRoute } from 'next';
import { siteConfig } from '@/lib/seo';

// Safe data fetching for build time
async function getProducts() {
  try {
    // Only fetch during runtime, not build time
    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL) {
      // During build time, return empty array to prevent ECONNREFUSED errors
      return [];
    }

    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/products`, {
      next: { revalidate: 3600 }, // Revalidate every hour
      headers: {
        'User-Agent': 'Sitemap Generator'
      }
    });

    if (!response.ok) {
      return [];
    }

    return await response.json();
  } catch (error) {
    // Silently handle errors during build time
    return [];
  }
}

async function getCategories() {
  try {
    // Only fetch during runtime, not build time
    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL) {
      // During build time, return empty array to prevent ECONNREFUSED errors
      return [];
    }

    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/categories`, {
      next: { revalidate: 3600 },
      headers: {
        'User-Agent': 'Sitemap Generator'
      }
    });

    if (!response.ok) {
      return [];
    }

    return await response.json();
  } catch (error) {
    // Silently handle errors during build time
    return [];
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = siteConfig.url;
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/products`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/sustainability`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/sustainable-farming`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/export-excellence`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/community-empowerment`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
  ];

  // Dynamic product pages
  const products = await getProducts();
  const productPages = products.map((product: any) => ({
    url: `${baseUrl}/products/${product.$id}`,
    lastModified: new Date(product.$updatedAt || product.$createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // Dynamic category pages
  const categories = await getCategories();
  const categoryPages = categories.map((category: any) => ({
    url: `${baseUrl}/categories/${category.$id}`,
    lastModified: new Date(category.$updatedAt || category.$createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }));

  return [
    ...staticPages,
    ...productPages,
    ...categoryPages,
  ];
}
