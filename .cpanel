# cPanel Configuration File
# This file contains instructions for cPanel deployment

# Directory Structure:
# /public_html/ - Main website directory (where files should be uploaded)
# /public_html/assets/ - Static assets (images, fonts, etc.)
# /public_html/_next/ - Next.js build assets

# Required cPanel Settings:
# 1. Enable "Index Documents" in File Manager
# 2. Set default document to "index.html"
# 3. Enable HTTPS redirect if SSL certificate is available
# 4. Configure custom error pages (404.html, 500.html)

# File Permissions:
# HTML files: 644
# Directories: 755
# Assets: 644

# MIME Types (add in cPanel if needed):
# .js - application/javascript
# .css - text/css
# .json - application/json
# .woff2 - font/woff2
# .woff - font/woff

# Redirect Rules for .htaccess:
# RewriteEngine On
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^(.*)$ /index.html [L]
