{"name": "afric-source", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "export": "next build", "start": "next start", "lint": "next lint", "setup:collections": "node scripts/setup-appwrite-collections.js", "verify:collections": "node scripts/verify-appwrite-collections.js", "fix:collections": "node scripts/fix-appwrite-collections.js", "admin:check": "node scripts/create-admin-user.js check", "admin:create": "node scripts/create-admin-user.js create", "admin:reset": "node scripts/create-admin-user.js reset", "migrate:api": "node scripts/migrate-to-client-api.js"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@types/bcrypt": "^5.0.2", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "next": "15.3.2", "node-appwrite": "^11.0.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/swiper": "^5.4.3", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "swiper": "^11.2.8", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5", "uuid": "^11.1.0"}}