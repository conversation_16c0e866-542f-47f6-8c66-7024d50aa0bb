# AfricSource cPanel Deployment Guide

## Overview
This guide will help you deploy your AfricSource Next.js application to cPanel hosting using static export and GitHub Actions CI/CD.

## Prerequisites
- cPanel hosting account with FTP/SFTP access
- GitHub repository with your code
- Appwrite backend service configured

## Deployment Strategy
We're using **Static Export** deployment because most cPanel hosting providers don't support Node.js applications. This means:
- ✅ All pages are pre-rendered as static HTML
- ✅ Client-side API calls to Appwrite backend
- ✅ Works on any web hosting provider
- ❌ No server-side API routes (replaced with client-side calls)

## Step 1: Configure GitHub Secrets

In your GitHub repository, go to **Settings > Secrets and variables > Actions** and add these secrets:

### FTP/SFTP Credentials:
```
CPANEL_FTP_SERVER=your-domain.com (or ftp.your-domain.com)
CPANEL_FTP_USERNAME=your-cpanel-username
CPANEL_FTP_PASSWORD=your-cpanel-password
```

### Appwrite Configuration:
```
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://your-appwrite-endpoint.com/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your-database-id
NEXT_PUBLIC_APPWRITE_COLLECTION_USERS=your-users-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS=your-products-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES=your-categories-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS=your-tags-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES=your-product-images-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS=your-specifications-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS=your-contact-submissions-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES=your-product-categories-collection-id
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS=your-product-tags-collection-id
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your-storage-bucket-id
NEXT_PUBLIC_APPWRITE_ASSETS_BUCKET_ID=your-assets-bucket-id
```

### Social Media Links:
```
NEXT_PUBLIC_SOCIAL_LINKEDIN=https://linkedin.com/company/africsource
NEXT_PUBLIC_SOCIAL_TWITTER=https://twitter.com/africsource
NEXT_PUBLIC_SOCIAL_INSTAGRAM=https://instagram.com/africsource
NEXT_PUBLIC_SOCIAL_FACEBOOK=https://facebook.com/africsource
NEXT_PUBLIC_SOCIAL_YOUTUBE=https://youtube.com/@africsource
NEXT_PUBLIC_SOCIAL_TIKTOK=https://tiktok.com/@africsource
```

## Step 2: Update Your Code

### Replace API Route Calls
Update your components to use the new client-side API:

```typescript
// Old (server-side API route)
const response = await fetch('/api/products');

// New (client-side Appwrite)
import { clientAPI } from '@/lib/client-api';
const response = await clientAPI.getProducts();
```

### Update Authentication
Replace NextAuth.js calls with Appwrite authentication:

```typescript
// Old
import { signIn, signOut } from 'next-auth/react';

// New
import { clientAPI } from '@/lib/client-api';
await clientAPI.login(email, password);
await clientAPI.logout();
```

## Step 3: Test Local Build

Before deploying, test the static export locally:

```bash
npm run export
cd out
npx serve .
```

Visit `http://localhost:3000` to test your static site.

## Step 4: Deploy

### Automatic Deployment (Recommended)
1. Push your code to the `production` branch
2. GitHub Actions will automatically build and deploy to your cPanel

### Manual Deployment
If you prefer manual deployment:

```bash
npm run export
```

Then upload the contents of the `out/` folder to your cPanel's `public_html/` directory.

## Step 5: cPanel Configuration

### File Manager Setup:
1. Log into cPanel
2. Open **File Manager**
3. Navigate to `public_html/`
4. Ensure the `.htaccess` file is present (it handles routing)
5. Set file permissions:
   - HTML files: 644
   - Directories: 755
   - .htaccess: 644

### Domain Configuration:
1. In cPanel, go to **Subdomains** or **Addon Domains**
2. Point your domain to the `public_html/` directory
3. Enable **Force HTTPS Redirect** if you have an SSL certificate

### Error Pages:
The deployment includes custom 404 and 500 error pages. Make sure they're accessible in your `public_html/` directory.

## Step 6: Verify Deployment

1. Visit your website
2. Test navigation between pages
3. Test contact form submission
4. Test product browsing
5. Test admin dashboard login (if applicable)

## Troubleshooting

### Common Issues:

**404 Errors on Page Refresh:**
- Ensure `.htaccess` file is in the root directory
- Check that mod_rewrite is enabled in cPanel

**Images Not Loading:**
- Verify Appwrite storage bucket permissions
- Check image URLs in the browser console

**API Calls Failing:**
- Verify all Appwrite environment variables are set correctly
- Check browser console for CORS errors
- Ensure Appwrite project allows your domain

**Build Failures:**
- Check GitHub Actions logs
- Verify all required secrets are set
- Ensure no TypeScript errors in your code

### Performance Optimization:

1. **Enable Gzip Compression** in cPanel
2. **Set up Cloudflare** for CDN and additional caching
3. **Optimize Images** before uploading to Appwrite
4. **Monitor Core Web Vitals** using Google PageSpeed Insights

## Maintenance

### Regular Tasks:
- Monitor GitHub Actions for failed deployments
- Update dependencies regularly
- Backup your Appwrite database
- Monitor website performance and uptime

### Updates:
To deploy updates, simply push to the `production` branch. The CI/CD pipeline will handle the rest.

## Support

If you encounter issues:
1. Check the GitHub Actions logs
2. Review cPanel error logs
3. Test the static export locally
4. Verify all environment variables are correct

## Alternative Deployment Options

If your cPanel supports Node.js applications, you can:
1. Remove `output: 'export'` from `next.config.ts`
2. Deploy the full Next.js application
3. Use server-side API routes instead of client-side calls

However, static export is recommended for better performance and compatibility.
