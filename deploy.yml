name: 🚀 Deploy Next.js App to FTP

on:
  push:
    branches:
      - test-branch 

jobs:
  web-deploy:
    name: 🎉 Deploy Next.js App
    runs-on: ubuntu-latest

    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v4

    - name: ⚙️ Setup Node.js environment
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 Install dependencies
      run: npm install

    - name: 🏗️ Build Next.js application
      run: npm run build

    - name: 📂 Sync files to FTP
      uses: SamKirkland/FTP-Deploy-Action@v4.3.5
      with:
        server: ${{ secrets.ftp_server }}
        username: ${{ secrets.ftp_username }}
        password: ${{ secrets.ftp_password }}
        local-dir: ./out/