/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // primary: {
        //   50: '#fdf6ed',
        //   100: '#f9e8d2',
        //   200: '#f3d0a5',
        //   300: '#eab978',
        //   400: '#e5a14b',
        //   500: '#d98c2e',
        //   600: '#c47125',
        //   700: '#a35721',
        //   800: '#844522',
        //   900: '#6c3a1f',
        //   950: '#462412',
        // },
        secondary: {
          50: '#f0f9f0',
          100: '#dcefdc',
          200: '#bde0bd',
          300: '#9dd09d',
          400: '#7ebe7e',
          500: '#5ea75e',
          600: '#4a8c4a',
          700: '#3d713d',
          800: '#345c34',
          900: '#2c4c2c',
          950: '#1a2e1a',
        },
        earth: {
          50: '#f8f6f4',
          100: '#e8e4df',
          200: '#d5cdc3',
          300: '#bbb0a1',
          400: '#a08f7a',
          500: '#8c7a63',
          600: '#746550',
          700: '#5d5142',
          800: '#4a4137',
          900: '#3d372f',
          950: '#28241f',
        },
        'deep-charcoal': 'var(--deep-charcoal)',
        'forest-green': 'var(--forest-green)',
        'earthy-cocoa': 'var(--earthy-cocoa)',
        'sun-gold': 'var(--sun-gold)',
        'harvest-green': 'var(--harvest-green)',
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)', 'system-ui', 'sans-serif'],
        mono: ['var(--font-geist-mono)', 'monospace'],
        display: ['var(--font-geist-sans)', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        '7xl': '4.5rem',
        '8xl': '6rem',
        '9xl': '8rem',
      },
      borderRadius: {
        'sm': '0.25rem',
        DEFAULT: '0.375rem',
        'md': '0.5rem',
        'lg': '0.75rem',
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
        'full': '9999px',
      },
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'soft': '0 4px 20px rgba(0, 0, 0, 0.08)',
        'glow': '0 0 15px rgba(229, 161, 75, 0.5)',
        'glow-green': '0 0 15px rgba(94, 167, 94, 0.5)',
      },
      spacing: {
        '72': '18rem',
        '80': '20rem',
        '96': '24rem',
        '128': '32rem',
      },
      height: {
        '1/2-screen': '50vh',
        '3/4-screen': '75vh',
        'screen-80': '80vh',
        'screen-90': '90vh',
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'fade-in-up': 'fadeInUp 0.7s ease-out',
        'fade-in-down': 'fadeInDown 0.7s ease-out',
        'fade-in-left': 'fadeInLeft 0.7s ease-out',
        'fade-in-right': 'fadeInRight 0.7s ease-out',
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bloom': 'bloom 0.5s ease-out',
        'slide-in-right': 'slideInRight 0.3s ease-out',
        'color-cycle': 'colorCycle 4.8s ease-in-out infinite',
        'logo-glow': 'logoGlow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInDown: {
          '0%': { opacity: '0', transform: 'translateY(-20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInLeft: {
          '0%': { opacity: '0', transform: 'translateX(-20px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        fadeInRight: {
          '0%': { opacity: '0', transform: 'translateX(20px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        bloom: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
          '100%': { transform: 'scale(1)' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        colorCycle: {
          '0%': { filter: 'hue-rotate(0deg) brightness(1)' },
          '16.66%': { filter: 'hue-rotate(60deg) brightness(1.1)' },
          '33.33%': { filter: 'hue-rotate(120deg) brightness(1)' },
          '50%': { filter: 'hue-rotate(180deg) brightness(1.1)' },
          '66.66%': { filter: 'hue-rotate(240deg) brightness(1)' },
          '83.33%': { filter: 'hue-rotate(300deg) brightness(1.1)' },
          '100%': { filter: 'hue-rotate(360deg) brightness(1)' },
        },
        logoGlow: {
          '0%': { filter: 'drop-shadow(0 0 5px rgba(255, 255, 255, 0.3))' },
          '100%': { filter: 'drop-shadow(0 0 20px rgba(255, 255, 255, 0.6))' },
        },
      },
      backgroundImage: {
        'grain': "url('/textures/grain.svg')",
        'paper': "url('/textures/paper.svg')",
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
