name: 🚀 Deploy Next.js App to cPanel

on:
  push:
    branches:
      - production # Change this to your main deployment branch

jobs:
  web-deploy:
    name: 🎉 Deploy Next.js App to cPanel
    runs-on: ubuntu-latest

    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v4

    - name: ⚙️ Setup Node.js environment
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 Install dependencies
      run: npm ci --only=production

    - name: 🔧 Create environment file
      run: |
        echo "NODE_ENV=production" > .env.local
        echo "NEXT_PUBLIC_APPWRITE_ENDPOINT=${{ secrets.NEXT_PUBLIC_APPWRITE_ENDPOINT }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_PROJECT_ID=${{ secrets.NEXT_PUBLIC_APPWRITE_PROJECT_ID }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_DATABASE_ID=${{ secrets.NEXT_PUBLIC_APPWRITE_DATABASE_ID }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_USERS=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS=${{ secrets.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=${{ secrets.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID }}" >> .env.local
        echo "NEXT_PUBLIC_APPWRITE_ASSETS_BUCKET_ID=${{ secrets.NEXT_PUBLIC_APPWRITE_ASSETS_BUCKET_ID }}" >> .env.local
        echo "NEXT_PUBLIC_SOCIAL_LINKEDIN=${{ secrets.NEXT_PUBLIC_SOCIAL_LINKEDIN }}" >> .env.local
        echo "NEXT_PUBLIC_SOCIAL_TWITTER=${{ secrets.NEXT_PUBLIC_SOCIAL_TWITTER }}" >> .env.local
        echo "NEXT_PUBLIC_SOCIAL_INSTAGRAM=${{ secrets.NEXT_PUBLIC_SOCIAL_INSTAGRAM }}" >> .env.local
        echo "NEXT_PUBLIC_SOCIAL_FACEBOOK=${{ secrets.NEXT_PUBLIC_SOCIAL_FACEBOOK }}" >> .env.local
        echo "NEXT_PUBLIC_SOCIAL_YOUTUBE=${{ secrets.NEXT_PUBLIC_SOCIAL_YOUTUBE }}" >> .env.local
        echo "NEXT_PUBLIC_SOCIAL_TIKTOK=${{ secrets.NEXT_PUBLIC_SOCIAL_TIKTOK }}" >> .env.local

    - name: 🏗 Build and Export Next.js application
      run: npm run export

    - name: 📂 Deploy to cPanel via FTP
      uses: SamKirkland/FTP-Deploy-Action@v4.3.5
      with:
        server: ${{ secrets.CPANEL_FTP_SERVER }}
        username: ${{ secrets.CPANEL_FTP_USERNAME }}
        password: ${{ secrets.CPANEL_FTP_PASSWORD }}
        protocol: ftps
        port: 21
        local-dir: ./out/
        server-dir: /public_html/
        exclude: |
          **/.git*
          **/.git*/**
          **/node_modules/**
          **/.next/**
          **/.env*
          **/README.md
          **/package*.json
          **/tsconfig.json
          **/next.config.*
          **/*.log