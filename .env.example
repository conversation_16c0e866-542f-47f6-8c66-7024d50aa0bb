# Environment Variables
NODE_ENV=

# Legacy NextAuth.js (replaced with Appwrite Auth)
NEXTAUTH_URL=
NEXTAUTH_SECRET=

# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=
NEXT_PUBLIC_APPWRITE_PROJECT_ID=
APPWRITE_API_KEY=

# Appwrite Database Configuration
NEXT_PUBLIC_APPWRITE_DATABASE_ID=

# Appwrite Collection IDs
NEXT_PUBLIC_APPWRITE_COLLECTION_USERS=
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS=
NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES=
NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS=
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES=
NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS=
NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS=
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES=
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS=

# Appwrite Storage
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=
NEXT_PUBLIC_APPWRITE_ASSETS_BUCKET_ID=


# Social Media Links
NEXT_PUBLIC_SOCIAL_LINKEDIN=
NEXT_PUBLIC_SOCIAL_TWITTER=
NEXT_PUBLIC_SOCIAL_INSTAGRAM=
NEXT_PUBLIC_SOCIAL_FACEBOOK=
NEXT_PUBLIC_SOCIAL_YOUTUBE=
NEXT_PUBLIC_SOCIAL_TIKTOK=
