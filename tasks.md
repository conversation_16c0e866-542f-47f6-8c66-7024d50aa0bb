# Website Implementation Tasks for AfricSource

## Legend
- [ ] Not Started
- [~] In Progress
- [x] Complete

---

## Content Refinement
- [ ] Review and polish all page copy for clarity, professionalism, and consistency (Home, About, Products, Sustainability, Contact)
- [ ] Replace all image placeholders (e.g., `image-url-placeholder`) with high-quality, relevant images
- [ ] Ensure all product specifications are accurate and up-to-date
- [ ] Add clear, benefit-focused CTAs to each page (e.g., "Request a Quote", "Contact Us")
- [ ] Refine headings and subheadings for better scan-ability
- [ ] Ensure consistent tone and formatting across all sections
- [ ] Add missing company details (e.g., team, certifications, awards) if available

## SEO & Metadata
- [ ] Write unique meta titles and descriptions for each page
- [ ] Add relevant keywords to page content and metadata
- [ ] Implement Open Graph and Twitter Card metadata for social sharing
- [ ] Add alt text to all images
- [ ] Ensure all URLs are SEO-friendly

## UI/UX Design
- [ ] Establish a design system (tokens for color, spacing, typography, breakpoints, shadows, border-radius, etc.)
- [ ] Define global layout containers and responsive grid system (Tailwind CSS best practices)
- [ ] Implement scalable typography and heading hierarchy (fluid scaling, semantic tags)
- [ ] Audit and ensure accessibility (keyboard navigation, focus states, ARIA, color contrast, skip links)
- [ ] Add micro-interactions and smooth transitions (button hover, CTA animation, section fade-ins)
- [ ] Design and implement reusable UI components (buttons, cards, modals, alerts, badges, etc.)
- [ ] Create a consistent, sticky navigation bar with mobile drawer support
- [ ] Implement visually distinct, benefit-focused CTAs (primary, secondary, ghost styles)
- [ ] Add testimonials, client logos, and trust signals (carousel or grid)
- [ ] Integrate FAQ and expandable sections for common questions
- [ ] Ensure all pages are mobile-first and fully responsive (test on multiple devices)
- [ ] Conduct regular accessibility audits and visual regression tests

## Frontend Development (Next.js)
- [~] Implement all pages: Home, About, Products, Sustainability, Contact using Next.js app router
- [ ] Build a modular component architecture (atomic design: atoms, molecules, organisms, templates, pages)
- [ ] Create a `/components` directory for shared UI elements
- [ ] Use layout components for consistent page structure (header, footer, main, sidebar)
- [ ] Integrate dynamic product data via API hooks (SWR/React Query)
- [ ] Implement smooth scrolling, section transitions, and page fade animations
- [ ] Add skeleton loaders and shimmer effects for images and async content
- [ ] Set up error boundaries and user-friendly error pages (404, 500)
- [ ] Add dark mode toggle and theme support
- [ ] Write unit and integration tests for components (Jest, React Testing Library)
- [ ] Document components and design tokens (Storybook or similar)

## Backend/API Development (Next.js API Routes)
- [ ] Structure `/api` endpoints with clear separation (products, contact, auth, uploads)
- [ ] Use schema validation (Zod/Yup) for all API inputs/outputs
- [ ] Implement rate limiting and input sanitization for public endpoints
- [ ] Add logging and error monitoring (Sentry or similar)
- [ ] Set up Prisma with environment-based config (SQLite dev, PostgreSQL prod)
- [ ] Write integration tests for API endpoints

## Dashboard & Authentication
- [ ] Create dashboard UI for product management (CRUD)
- [ ] Protect dashboard with authentication (NextAuth.js or similar)
- [ ] Add user roles (admin, staff)
- [ ] Implement login/logout functionality
- [ ] Use NextAuth.js with JWT/session for secure authentication
- [ ] Implement role-based access control (admin, staff, viewer)
- [ ] Protect dashboard and API routes with middleware
- [ ] Add audit logging for admin actions
- [ ] Implement password reset and email verification flows

## Contact Form Setup
- [ ] Build a contact form with fields: Name, Email, Message, Phone (optional)
- [ ] Validate form inputs and display user-friendly error messages
- [ ] Integrate form with backend/email service (e.g., Nodemailer, Formspree)
- [ ] Add success/failure notifications after submission
- [ ] Implement spam protection (honeypot, reCAPTCHA)

## Image & Asset Management
- [ ] Automate image optimization (next/image, sharp, or similar)
- [ ] Integrate with cloud storage (MinIO/S3) for production asset management
- [ ] Add admin UI for asset upload, preview, and management
- [ ] Implement fallback images and error handling for broken links

## Deployment & Hosting
- [ ] Set up CI/CD pipeline (GitHub Actions or Vercel/Netlify built-in)
- [ ] Add automated linting, type-checking, and test runs on PRs
- [ ] Configure preview deployments for feature branches
- [ ] Automate environment variable management and secrets rotation
- [ ] Monitor performance and uptime (Vercel Analytics, Sentry, or similar)
- [ ] Set up automated accessibility and Lighthouse audits in CI

---

# content-and-pages.md Review Summary & Suggestions

**Strengths:**
- Clear structure with dedicated sections for Home, About, Products, Sustainability, and Contact
- Benefit-focused copy and strong value propositions
- Good use of bullet points for readability
- Placeholders for images and CTAs are present

**Areas for Improvement:**
- **Image Placeholders:** All product images use `image-url-placeholder`—replace with real, high-quality images
- **CTAs:** Some CTAs are in brackets (e.g., [Request a Quote], [Contact Form])—ensure these are implemented as visible, actionable buttons
- **Missing Sections:** Consider adding a FAQ, testimonials, or client logos for credibility
- **Contact Form:** No fields specified—define required fields (Name, Email, Message, etc.)
- **Formatting:** Ensure consistent use of headings and bullet points; some sections could use more subheadings for clarity
- **SEO:** No meta descriptions or keywords—add for each page
- **Social Proof:** Add certifications, awards, or partner logos if available
- **Accessibility:** Ensure all images have descriptive alt text and content is accessible
- **Conversion Optimization:** Add more benefit-focused CTAs and ensure each page has a clear next step

**Overall:**
The content is well-structured and covers all essential sections for a commodity export company. Addressing the above points will improve clarity, professionalism, and conversion potential.
